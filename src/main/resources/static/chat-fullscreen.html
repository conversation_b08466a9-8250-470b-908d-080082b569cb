<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话助手 - v2025.08.04</title>
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script>
        // 等待marked库加载完成
        window.addEventListener('load', function() {
            console.log('页面加载完成，marked库状态:', typeof marked);
            if (typeof marked !== 'undefined') {
                console.log('marked库加载成功');
                // 测试marked库
                try {
                    const testResult = marked.parse('**测试**');
                    console.log('marked测试结果:', testResult);
                } catch (e) {
                    console.error('marked测试失败:', e);
                }
            } else {
                console.error('marked库加载失败');
            }
        });
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            height: 100vh;
            overflow: hidden;
            background: #f5f5f5;
        }

        .chat-container {
            display: flex;
            height: 100vh;
        }

        /* 左侧会话列表 */
        .sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        @media (min-width: 1400px) {
            .sidebar {
                width: 400px;
            }
        }

        @media (min-width: 1800px) {
            .sidebar {
                width: 450px;
            }
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .agent-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .agent-details h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .agent-details p {
            color: #666;
            font-size: 12px;
        }

        .new-chat-btn {
            width: 100%;
            padding: 10px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .new-chat-btn:hover {
            background: #40a9ff;
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .conversation-item {
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 8px;
            transition: background 0.3s;
            border: 1px solid transparent;
        }

        .conversation-item:hover {
            background: #f0f0f0;
        }

        .conversation-item.active {
            background: #e6f7ff;
            border-color: #1890ff;
        }

        .conversation-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .conversation-preview {
            font-size: 12px;
            color: #999;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-time {
            font-size: 11px;
            color: #ccc;
            margin-top: 4px;
        }

        /* 右侧对话区域 */
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-title {
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }

        .chat-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #52c41a;
            color: white;
        }

        .message.assistant .message-avatar {
            background: #1890ff;
            color: white;
        }

        .message-content {
            max-width: 60%;
            padding: 14px 18px;
            border-radius: 12px;
            line-height: 1.6;
            word-wrap: break-word;
            font-size: 15px;
        }

        @media (min-width: 1400px) {
            .message-content {
                max-width: 55%;
                padding: 16px 20px;
            }
        }

        .message.user .message-content {
            background: #1890ff;
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message.assistant.streaming .message-content {
            border-left: 3px solid #1890ff;
            animation: pulse 1s infinite;
        }

        /* Markdown样式 */
        .message-content h1, .message-content h2, .message-content h3 {
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .message-content h1 {
            font-size: 1.5em;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 8px;
        }

        .message-content h2 {
            font-size: 1.3em;
            color: #333;
        }

        .message-content h3 {
            font-size: 1.1em;
            color: #555;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 4px 0;
        }

        .message-content strong {
            font-weight: 600;
            color: #2c3e50;
        }

        .message-content p {
            margin: 8px 0;
        }

        .message-content code {
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: #f8f8f8;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
            text-align: center;
        }

        .chat-input-container {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .chat-input-wrapper {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            resize: none;
            font-size: 14px;
            line-height: 1.4;
            font-family: inherit;
        }

        .chat-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .send-btn {
            padding: 12px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .send-btn:hover:not(:disabled) {
            background: #40a9ff;
        }

        .send-btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #999;
            text-align: center;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 16px;
            opacity: 0.3;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #999;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 300px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 280px;
            }

            .chat-messages {
                padding: 15px;
            }

            .message-content {
                max-width: 80%;
            }
        }

        @media (max-width: 600px) {
            .sidebar {
                position: absolute;
                left: -350px;
                z-index: 1000;
                transition: left 0.3s;
                width: 300px;
            }

            .sidebar.show {
                left: 0;
            }

            .chat-main {
                width: 100%;
            }

            .message-content {
                max-width: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- 左侧会话列表 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="agent-info">
                    <div class="agent-avatar" id="agentAvatar">AI</div>
                    <div class="agent-details">
                        <h3 id="agentName">加载中...</h3>
                        <p id="agentDescription">正在加载Agent信息</p>
                    </div>
                </div>
                <button class="new-chat-btn" onclick="startNewConversation()">+ 新对话</button>
            </div>
            
            <div class="conversations-list" id="conversationsList">
                <div class="loading">加载会话列表...</div>
            </div>
        </div>

        <!-- 右侧对话区域 -->
        <div class="chat-main">
            <div class="chat-header">
                <div class="chat-title" id="chatTitle">选择或创建新对话 <small style="color: #666;">(v2025.08.04-Markdown)</small></div>
                <div class="chat-actions">
                    <button class="btn btn-secondary" onclick="window.close()">关闭窗口</button>
                    <button class="btn btn-secondary" onclick="window.open('agent-management.html', '_blank')">管理中心</button>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="empty-state">
                    <div class="empty-icon">💬</div>
                    <h3>开始新的对话</h3>
                    <p>点击"新对话"按钮或选择历史会话开始聊天</p>
                </div>
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        id="messageInput" 
                        class="chat-input" 
                        placeholder="输入你的消息..." 
                        rows="1"
                        disabled></textarea>
                    <button id="sendBtn" class="send-btn" onclick="sendMessage()" disabled>发送</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAgentId = null;
        let currentConversationId = null;
        let agentInfo = null;
        let conversations = [];

        // 简单的Markdown解析函数（备用）
        function simpleMarkdownParse(text) {
            return text
                // 标题
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                // 列表
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                .replace(/^(\d+)\. (.*$)/gm, '<li>$2</li>')
                // 换行
                .replace(/\n/g, '<br>');
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 测试marked库是否正确加载
            console.log('页面加载完成');
            console.log('marked库状态:', typeof marked);
            if (typeof marked !== 'undefined') {
                console.log('marked库版本:', marked.version || 'unknown');
                // 测试简单的Markdown渲染
                try {
                    const testResult = marked.parse('**测试**');
                    console.log('Markdown测试渲染结果:', testResult);
                } catch (e) {
                    console.error('Markdown测试渲染失败:', e);
                }
            }

            const urlParams = new URLSearchParams(window.location.search);
            currentAgentId = urlParams.get('agentId');
            
            if (!currentAgentId) {
                alert('缺少Agent ID参数');
                window.close();
                return;
            }

            initializePage();
        });

        // 初始化页面
        async function initializePage() {
            try {
                await loadAgentInfo();
                await loadConversations();
                setupEventListeners();
            } catch (error) {
                console.error('初始化失败:', error);
                alert('页面初始化失败，请刷新重试');
            }
        }

        // 加载Agent信息
        async function loadAgentInfo() {
            try {
                const response = await fetch(`/v1/agents/${currentAgentId}`);
                if (response.ok) {
                    agentInfo = await response.json();
                    updateAgentDisplay();
                } else {
                    throw new Error('加载Agent信息失败');
                }
            } catch (error) {
                console.error('加载Agent信息失败:', error);
                document.getElementById('agentName').textContent = 'Agent加载失败';
                document.getElementById('agentDescription').textContent = '请刷新页面重试';
            }
        }

        // 更新Agent显示
        function updateAgentDisplay() {
            if (!agentInfo) return;
            
            document.getElementById('agentName').textContent = agentInfo.name || 'AI助手';
            document.getElementById('agentDescription').textContent = agentInfo.description || '智能对话助手';
            document.getElementById('agentAvatar').textContent = (agentInfo.name || 'AI').charAt(0).toUpperCase();
            document.title = `${agentInfo.name || 'AI助手'} - 对话`;
        }

        // 加载会话列表
        async function loadConversations() {
            try {
                const response = await fetch(`/v1/agents/${currentAgentId}/conversations?userId=1`);
                if (response.ok) {
                    conversations = await response.json();
                    displayConversations();
                } else {
                    throw new Error('加载会话列表失败');
                }
            } catch (error) {
                console.error('加载会话列表失败:', error);
                document.getElementById('conversationsList').innerHTML = 
                    '<div class="loading">加载会话列表失败，请刷新重试</div>';
            }
        }

        // 显示会话列表
        function displayConversations() {
            const listContainer = document.getElementById('conversationsList');
            
            if (conversations.length === 0) {
                listContainer.innerHTML = '<div class="loading">暂无历史会话</div>';
                return;
            }

            listContainer.innerHTML = conversations.map(conv => `
                <div class="conversation-item" onclick="selectConversation('${conv.uuid}')">
                    <div class="conversation-title">${conv.title || '新对话'}</div>
                    <div class="conversation-preview">${conv.lastMessage || '暂无消息'}</div>
                    <div class="conversation-time">${formatTime(conv.updatedAt)}</div>
                </div>
            `).join('');
        }

        // 选择会话
        async function selectConversation(conversationId) {
            currentConversationId = conversationId;
            
            // 更新UI状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            
            // 启用输入
            enableChatInput();
            
            // 加载会话消息
            await loadConversationMessages(conversationId);
        }

        // 加载会话消息
        async function loadConversationMessages(conversationId) {
            try {
                document.getElementById('chatMessages').innerHTML = '<div class="loading">加载消息中...</div>';
                
                const response = await fetch(`/v1/conversations/${conversationId}/messages`);
                if (response.ok) {
                    const messages = await response.json();
                    displayMessages(messages);
                    updateChatTitle();
                } else {
                    throw new Error('加载消息失败');
                }
            } catch (error) {
                console.error('加载消息失败:', error);
                document.getElementById('chatMessages').innerHTML = 
                    '<div class="loading">加载消息失败，请重试</div>';
            }
        }

        // 显示消息
        function displayMessages(messages) {
            const messagesContainer = document.getElementById('chatMessages');
            
            if (messages.length === 0) {
                messagesContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">💬</div>
                        <h3>开始对话</h3>
                        <p>发送第一条消息开始与AI助手对话</p>
                    </div>
                `;
                return;
            }

            messagesContainer.innerHTML = messages.map(msg => {
                const role = msg.role.toLowerCase(); // 转换为小写
                return `
                    <div class="message ${role}">
                        <div class="message-avatar">${role === 'user' ? 'U' : 'AI'}</div>
                        <div class="message-content">${msg.content}</div>
                    </div>
                `;
            }).join('');
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 更新聊天标题
        function updateChatTitle() {
            const conversation = conversations.find(c => c.uuid === currentConversationId);
            document.getElementById('chatTitle').textContent = 
                conversation ? (conversation.title || '对话') : '对话';
        }

        // 开始新对话
        function startNewConversation() {
            currentConversationId = null;
            
            // 清除选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 清空消息区域
            document.getElementById('chatMessages').innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">💬</div>
                    <h3>新对话</h3>
                    <p>发送第一条消息开始与AI助手对话</p>
                </div>
            `;
            
            // 更新标题
            document.getElementById('chatTitle').textContent = '新对话';
            
            // 启用输入
            enableChatInput();
            
            // 聚焦输入框
            document.getElementById('messageInput').focus();
        }

        // 启用聊天输入
        function enableChatInput() {
            document.getElementById('messageInput').disabled = false;
            document.getElementById('sendBtn').disabled = false;
        }

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 清空输入框并禁用
            input.value = '';
            input.disabled = true;
            document.getElementById('sendBtn').disabled = true;
            
            // 添加用户消息到界面
            addMessageToUI('user', message);
            
            // 添加AI消息占位符
            const assistantMessageId = addMessageToUI('assistant', '', true);
            
            try {
                // 发送流式请求
                await sendStreamMessage(message, assistantMessageId);
            } catch (error) {
                console.error('发送消息失败:', error);
                updateMessageContent(assistantMessageId, '抱歉，发送消息失败，请重试。', false);
            } finally {
                // 重新启用输入
                input.disabled = false;
                document.getElementById('sendBtn').disabled = false;
                input.focus();
            }
        }

        // 发送流式消息
        async function sendStreamMessage(message, assistantMessageId) {
            const response = await fetch(`/v1/agents/${currentAgentId}/execute/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify({
                    query: message,
                    userId: 1,
                    conversationId: currentConversationId,
                    stream: true
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullContent = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        const data = line.slice(5);
                        if (data === '[DONE]') {
                            updateMessageContent(assistantMessageId, fullContent, false);
                            // 重新加载会话列表
                            await loadConversations();
                            return;
                        }

                        // ✅ 纯Markdown格式处理 - 无JSON解析 - v2025.08.04
                        console.log('✅ 收到Markdown数据:', data);
                        console.log('🔍 数据类型:', typeof data, '长度:', data.length);

                        if (data && data.trim() !== '') {
                            fullContent += data;
                            updateMessageContent(assistantMessageId, fullContent, true);
                            console.log('📝 累积内容长度:', fullContent.length);
                        } else {
                            console.log('⚠️ 收到空数据或换行符');
                        }
                    }
                }
            }
        }

        // 添加消息到UI
        function addMessageToUI(role, content, streaming = false) {
            const messagesContainer = document.getElementById('chatMessages');
            
            // 如果是空状态，先清空
            if (messagesContainer.querySelector('.empty-state')) {
                messagesContainer.innerHTML = '';
            }
            
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role} ${streaming ? 'streaming' : ''}`;
            messageDiv.id = messageId;
            messageDiv.innerHTML = `
                <div class="message-avatar">${role === 'user' ? 'U' : 'AI'}</div>
                <div class="message-content">${content}</div>
            `;

            // 调试日志
            console.log('创建消息元素:', {
                messageId,
                role,
                content,
                streaming,
                className: messageDiv.className
            });

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            return messageId;
        }

        // 更新消息内容
        function updateMessageContent(messageId, content, streaming) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const contentElement = messageElement.querySelector('.message-content');

                // 使用Markdown渲染
                if (typeof marked !== 'undefined' && marked.parse) {
                    try {
                        const renderedContent = marked.parse(content);
                        contentElement.innerHTML = renderedContent;
                        console.log('✅ Markdown渲染成功');
                    } catch (error) {
                        console.error('❌ Markdown渲染失败:', error);
                        contentElement.textContent = content;
                    }
                } else {
                    console.warn('⚠️ marked库未加载或不可用，使用纯文本显示');
                    contentElement.textContent = content;

                    // 尝试简单的Markdown解析
                    if (content.includes('**') || content.includes('#') || content.includes('-')) {
                        console.log('🔧 尝试简单Markdown解析');
                        contentElement.innerHTML = simpleMarkdownParse(content);
                    }
                }

                if (!streaming) {
                    messageElement.classList.remove('streaming');
                }

                // 调试日志
                console.log('更新消息内容:', {
                    messageId,
                    content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
                    streaming,
                    className: messageElement.className
                });
            } else {
                console.error('找不到消息元素:', messageId);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 输入框回车发送
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // 自动调整输入框高度
            document.getElementById('messageInput').addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        // 工具函数
        function formatTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            return date.toLocaleDateString('zh-CN');
        }
    </script>
</body>
</html>
