<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Server简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-case {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.success {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        .status.error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 MCP Server简单测试</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>这个页面专门测试MCP Server的直接调用功能，不依赖LLM服务。</p>
            <p><strong>MCP Server:</strong> http://localhost:8081/mcp</p>
            <p><strong>测试目标:</strong> 验证Agent系统能否正确调用MCP Server的工具</p>
        </div>

        <div class="test-section">
            <h3>🔍 1. 检查MCP Server状态</h3>
            <button onclick="checkMcpServerStatus()">检查MCP Server</button>
            <div id="statusResult"></div>
        </div>

        <div class="test-section">
            <h3>🧮 2. 数学计算测试</h3>
            
            <div class="test-case">
                <h4>加法测试: 3 + 2</h4>
                <button onclick="testMcpAdd()">测试加法</button>
                <span id="addStatus" class="status"></span>
                <div id="addResult"></div>
            </div>
            
            <div class="test-case">
                <h4>减法测试: 10 - 3</h4>
                <button onclick="testMcpSubtract()">测试减法</button>
                <span id="subtractStatus" class="status"></span>
                <div id="subtractResult"></div>
            </div>
            
            <div class="test-case">
                <h4>乘法测试: 4 × 5</h4>
                <button onclick="testMcpMultiply()">测试乘法</button>
                <span id="multiplyStatus" class="status"></span>
                <div id="multiplyResult"></div>
            </div>
            
            <div class="test-case">
                <h4>除法测试: 20 ÷ 4</h4>
                <button onclick="testMcpDivide()">测试除法</button>
                <span id="divideStatus" class="status"></span>
                <div id="divideResult"></div>
            </div>
            
            <div class="test-case">
                <h4>幂运算测试: 2^3</h4>
                <button onclick="testMcpPower()">测试幂运算</button>
                <span id="powerStatus" class="status"></span>
                <div id="powerResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌤️ 3. 天气查询测试</h3>
            
            <div class="test-case">
                <h4>北京天气查询</h4>
                <button onclick="testMcpWeather()">查询北京天气</button>
                <span id="weatherStatus" class="status"></span>
                <div id="weatherResult"></div>
            </div>
            
            <div class="test-case">
                <h4>支持的城市列表</h4>
                <button onclick="testMcpCities()">获取城市列表</button>
                <span id="citiesStatus" class="status"></span>
                <div id="citiesResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 4. 测试总结</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="summaryResult"></div>
        </div>
    </div>

    <script>
        // 显示结果
        function showResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            const resultText = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
            element.innerHTML = `<div class="result ${isError ? 'error' : ''}">${resultText}</div>`;
        }

        // 更新状态
        function updateStatus(statusId, success) {
            const element = document.getElementById(statusId);
            element.textContent = success ? '✅ 成功' : '❌ 失败';
            element.className = `status ${success ? 'success' : 'error'}`;
        }

        // 检查MCP Server状态
        async function checkMcpServerStatus() {
            try {
                // 直接调用MCP Server初始化
                const response = await fetch('http://localhost:8081/mcp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        jsonrpc: "2.0",
                        id: 1,
                        method: "initialize",
                        params: {
                            protocolVersion: "2024-11-05",
                            capabilities: {},
                            clientInfo: {
                                name: "test-client",
                                version: "1.0.0"
                            }
                        }
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('statusResult', {
                        success: true,
                        message: 'MCP Server连接正常',
                        serverInfo: result.result
                    });
                } else {
                    showResult('statusResult', {
                        success: false,
                        error: `HTTP ${response.status}: ${response.statusText}`
                    }, true);
                }
            } catch (error) {
                showResult('statusResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 测试MCP加法
        async function testMcpAdd() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ a: 3, b: 2 })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('addResult', result);
                    updateStatus('addStatus', true);
                } else {
                    const error = await response.text();
                    showResult('addResult', error, true);
                    updateStatus('addStatus', false);
                }
            } catch (error) {
                showResult('addResult', error.message, true);
                updateStatus('addStatus', false);
            }
        }

        // 测试MCP减法
        async function testMcpSubtract() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/subtract', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ a: 10, b: 3 })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('subtractResult', result);
                    updateStatus('subtractStatus', true);
                } else {
                    const error = await response.text();
                    showResult('subtractResult', error, true);
                    updateStatus('subtractStatus', false);
                }
            } catch (error) {
                showResult('subtractResult', error.message, true);
                updateStatus('subtractStatus', false);
            }
        }

        // 测试MCP乘法
        async function testMcpMultiply() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/multiply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ a: 4, b: 5 })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('multiplyResult', result);
                    updateStatus('multiplyStatus', true);
                } else {
                    const error = await response.text();
                    showResult('multiplyResult', error, true);
                    updateStatus('multiplyStatus', false);
                }
            } catch (error) {
                showResult('multiplyResult', error.message, true);
                updateStatus('multiplyStatus', false);
            }
        }

        // 测试MCP除法
        async function testMcpDivide() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/divide', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ a: 20, b: 4 })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('divideResult', result);
                    updateStatus('divideStatus', true);
                } else {
                    const error = await response.text();
                    showResult('divideResult', error, true);
                    updateStatus('divideStatus', false);
                }
            } catch (error) {
                showResult('divideResult', error.message, true);
                updateStatus('divideStatus', false);
            }
        }

        // 测试MCP幂运算
        async function testMcpPower() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/power', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ base: 2, exponent: 3 })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('powerResult', result);
                    updateStatus('powerStatus', true);
                } else {
                    const error = await response.text();
                    showResult('powerResult', error, true);
                    updateStatus('powerStatus', false);
                }
            } catch (error) {
                showResult('powerResult', error.message, true);
                updateStatus('powerStatus', false);
            }
        }

        // 测试MCP天气查询
        async function testMcpWeather() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/get_weather', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ city: "北京" })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('weatherResult', result);
                    updateStatus('weatherStatus', true);
                } else {
                    const error = await response.text();
                    showResult('weatherResult', error, true);
                    updateStatus('weatherStatus', false);
                }
            } catch (error) {
                showResult('weatherResult', error.message, true);
                updateStatus('weatherStatus', false);
            }
        }

        // 测试MCP城市列表
        async function testMcpCities() {
            try {
                const response = await fetch('/v1/mcp-servers/1/tools/get_supported_cities', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('citiesResult', result);
                    updateStatus('citiesStatus', true);
                } else {
                    const error = await response.text();
                    showResult('citiesResult', error, true);
                    updateStatus('citiesStatus', false);
                }
            } catch (error) {
                showResult('citiesResult', error.message, true);
                updateStatus('citiesStatus', false);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showResult('summaryResult', '🔄 正在运行所有测试...');
            
            await checkMcpServerStatus();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpAdd();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpSubtract();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpMultiply();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpDivide();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpPower();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpWeather();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMcpCities();
            
            // 统计结果
            const successElements = document.querySelectorAll('.status.success');
            const errorElements = document.querySelectorAll('.status.error');
            
            showResult('summaryResult', {
                message: '所有测试完成',
                totalTests: successElements.length + errorElements.length,
                successCount: successElements.length,
                errorCount: errorElements.length,
                successRate: `${Math.round((successElements.length / (successElements.length + errorElements.length)) * 100)}%`
            });
        }

        // 页面加载时自动检查MCP Server状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MCP Server简单测试页面加载完成');
            checkMcpServerStatus();
        });
    </script>
</body>
</html>
