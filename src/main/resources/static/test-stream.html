<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式输出测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-area {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .output {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🧪 Agent V3 流式输出测试</h1>
    
    <div class="test-area">
        <h2>1. 创建测试Agent</h2>
        <input type="text" id="agentName" placeholder="Agent名称" value="流式测试Agent">
        <textarea id="systemPrompt" placeholder="系统提示词" rows="3">你是一个友好的AI助手。请用简洁明了的语言回答用户问题，并且在回答中包含一些有趣的细节。</textarea>
        <button onclick="createTestAgent()">创建Agent</button>
        <div id="createStatus"></div>
    </div>
    
    <div class="test-area">
        <h2>2. 测试流式输出</h2>
        <input type="number" id="agentId" placeholder="Agent ID" value="1">
        <textarea id="testMessage" placeholder="测试消息" rows="2">请详细介绍一下人工智能的发展历史，包括重要的里程碑事件。</textarea>
        <button onclick="testStream()" id="streamBtn">开始流式测试</button>
        <button onclick="testNormal()" id="normalBtn">普通测试对比</button>
        <button onclick="clearOutput()">清空输出</button>
        
        <h3>流式输出结果：</h3>
        <div id="streamOutput" class="output">等待测试...</div>
        
        <h3>普通输出结果：</h3>
        <div id="normalOutput" class="output">等待测试...</div>
    </div>
    
    <script>
        let testAgentId = null;
        
        async function createTestAgent() {
            const statusDiv = document.getElementById('createStatus');
            statusDiv.innerHTML = '<div class="status">创建中...</div>';
            
            const agentData = {
                name: document.getElementById('agentName').value,
                mode: 'FUNCTION_CALLING',
                systemPrompt: document.getElementById('systemPrompt').value,
                openingStatement: '你好！我是测试Agent，准备为你提供流式回答。',
                creatorId: 1,
                modelConfig: {
                    modelName: 'qwen-turbo',
                    temperature: 0.7,
                    maxTokens: 2000,
                    topP: 0.9,
                    frequencyPenalty: 0.0,
                    presencePenalty: 0.0,
                    streamEnabled: true
                }
            };
            
            try {
                const response = await fetch('/v1/agents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(agentData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    testAgentId = result.id;
                    document.getElementById('agentId').value = result.id;
                    statusDiv.innerHTML = `<div class="status success">Agent创建成功！ID: ${result.id}</div>`;
                } else {
                    const error = await response.text();
                    statusDiv.innerHTML = `<div class="status error">创建失败: ${error}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">网络错误: ${error.message}</div>`;
            }
        }
        
        async function testStream() {
            const agentId = document.getElementById('agentId').value;
            const message = document.getElementById('testMessage').value;
            const outputDiv = document.getElementById('streamOutput');
            const streamBtn = document.getElementById('streamBtn');
            
            if (!agentId || !message) {
                alert('请填写Agent ID和测试消息');
                return;
            }
            
            streamBtn.disabled = true;
            streamBtn.textContent = '流式输出中...';
            outputDiv.textContent = '';
            
            const requestData = {
                query: message,
                userId: 1,
                stream: true
            };
            
            try {
                const response = await fetch(`/v1/agents/${agentId}/execute/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';
                
                outputDiv.textContent = '[开始流式输出]\n\n';
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                outputDiv.textContent += '\n\n[流式输出完成]';
                                return;
                            }
                            
                            // 处理纯Markdown格式数据
                            if (data && data.trim() !== '') {
                                fullContent += data;
                                outputDiv.textContent = '[开始流式输出]\n\n' + fullContent + '▋';
                                outputDiv.scrollTop = outputDiv.scrollHeight;
                            }
                        }
                    }
                }
            } catch (error) {
                outputDiv.textContent = `流式输出失败: ${error.message}`;
            } finally {
                streamBtn.disabled = false;
                streamBtn.textContent = '开始流式测试';
            }
        }
        
        async function testNormal() {
            const agentId = document.getElementById('agentId').value;
            const message = document.getElementById('testMessage').value;
            const outputDiv = document.getElementById('normalOutput');
            const normalBtn = document.getElementById('normalBtn');
            
            if (!agentId || !message) {
                alert('请填写Agent ID和测试消息');
                return;
            }
            
            normalBtn.disabled = true;
            normalBtn.textContent = '执行中...';
            outputDiv.textContent = '正在执行普通调用...';
            
            const requestData = {
                query: message,
                userId: 1
            };
            
            try {
                const startTime = Date.now();
                const response = await fetch(`/v1/agents/${agentId}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const endTime = Date.now();
                
                if (response.ok) {
                    const result = await response.json();
                    outputDiv.textContent = `[普通调用结果 - 耗时: ${endTime - startTime}ms]\n\n${result.content}`;
                } else {
                    const error = await response.text();
                    outputDiv.textContent = `普通调用失败: ${error}`;
                }
            } catch (error) {
                outputDiv.textContent = `网络错误: ${error.message}`;
            } finally {
                normalBtn.disabled = false;
                normalBtn.textContent = '普通测试对比';
            }
        }
        
        function clearOutput() {
            document.getElementById('streamOutput').textContent = '等待测试...';
            document.getElementById('normalOutput').textContent = '等待测试...';
        }
    </script>
</body>
</html>
