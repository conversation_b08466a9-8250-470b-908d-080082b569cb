<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息显示调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #52c41a;
            color: white;
        }

        .message.assistant .message-avatar {
            background: #1890ff;
            color: white;
        }

        .message-content {
            max-width: 60%;
            padding: 14px 18px;
            border-radius: 12px;
            line-height: 1.6;
            word-wrap: break-word;
            font-size: 15px;
        }

        .message.user .message-content {
            background: #1890ff;
            color: white;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message.assistant.streaming .message-content {
            border-left: 3px solid #1890ff;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .debug-info {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        #chatMessages {
            border: 1px solid #ddd;
            padding: 20px;
            min-height: 300px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <h1>消息显示调试</h1>
    
    <div class="debug-info">
        <strong>测试步骤：</strong><br>
        1. 点击"添加用户消息"按钮<br>
        2. 点击"添加AI消息"按钮<br>
        3. 观察消息是否正确显示在对应位置
    </div>

    <button onclick="addUserMessage()">添加用户消息</button>
    <button onclick="addAssistantMessage()">添加AI消息</button>
    <button onclick="clearMessages()">清空消息</button>
    <button onclick="testStreamingMessage()">测试流式消息</button>

    <div id="chatMessages"></div>

    <div class="debug-info" id="debugInfo">调试信息将显示在这里</div>

    <script>
        let messageCounter = 1;

        function addUserMessage() {
            const message = `用户消息 ${messageCounter++}`;
            const messageId = addMessageToUI('user', message);
            updateDebugInfo(`添加用户消息: ${message}, ID: ${messageId}`);
        }

        function addAssistantMessage() {
            const message = `AI回复 ${messageCounter++}`;
            const messageId = addMessageToUI('assistant', message);
            updateDebugInfo(`添加AI消息: ${message}, ID: ${messageId}`);
        }

        function clearMessages() {
            document.getElementById('chatMessages').innerHTML = '';
            updateDebugInfo('清空所有消息');
        }

        function testStreamingMessage() {
            const messageId = addMessageToUI('assistant', '', true);
            updateDebugInfo(`创建流式消息占位符, ID: ${messageId}`);
            
            let content = '';
            const words = ['你好', '！', '我是', 'AI', '助手', '，', '很高兴', '为你', '服务', '！'];
            let index = 0;
            
            const interval = setInterval(() => {
                if (index < words.length) {
                    content += words[index];
                    updateMessageContent(messageId, content, true);
                    updateDebugInfo(`更新流式消息: ${content}`);
                    index++;
                } else {
                    updateMessageContent(messageId, content, false);
                    updateDebugInfo(`流式消息完成: ${content}`);
                    clearInterval(interval);
                }
            }, 200);
        }

        function addMessageToUI(role, content, streaming = false) {
            const messagesContainer = document.getElementById('chatMessages');
            
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role} ${streaming ? 'streaming' : ''}`;
            messageDiv.id = messageId;
            messageDiv.innerHTML = `
                <div class="message-avatar">${role === 'user' ? 'U' : 'AI'}</div>
                <div class="message-content">${content}</div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            
            // 调试信息
            console.log(`创建消息元素:`, {
                messageId,
                role,
                content,
                streaming,
                className: messageDiv.className
            });
            
            return messageId;
        }

        function updateMessageContent(messageId, content, streaming) {
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const contentElement = messageElement.querySelector('.message-content');
                contentElement.textContent = content;
                
                if (!streaming) {
                    messageElement.classList.remove('streaming');
                }
                
                // 调试信息
                console.log(`更新消息内容:`, {
                    messageId,
                    content,
                    streaming,
                    className: messageElement.className
                });
            } else {
                console.error(`找不到消息元素: ${messageId}`);
            }
        }

        function updateDebugInfo(info) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `<br>[${timestamp}] ${info}`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        // 页面加载时显示初始状态
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo('页面加载完成，准备测试消息显示');
        });
    </script>
</body>
</html>
