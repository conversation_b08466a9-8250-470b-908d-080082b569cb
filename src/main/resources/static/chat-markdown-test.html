<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown聊天测试 - 2025.08.04</title>
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .chat-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .messages {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .message.user {
            background: #e3f2fd;
            margin-left: 20%;
        }
        .message.assistant {
            background: white;
            border: 1px solid #e0e0e0;
            margin-right: 20%;
        }
        .message.streaming {
            border-left: 3px solid #2196f3;
        }
        .input-area {
            display: flex;
            gap: 10px;
        }
        input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            padding: 12px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        button:hover {
            background: #1976d2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
        /* Markdown样式 */
        .message h1, .message h2, .message h3 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        .message h1 { font-size: 1.4em; border-bottom: 2px solid #e0e0e0; padding-bottom: 5px; }
        .message h2 { font-size: 1.2em; }
        .message h3 { font-size: 1.1em; }
        .message ul, .message ol { margin: 8px 0; padding-left: 20px; }
        .message li { margin: 3px 0; }
        .message strong { color: #2c3e50; font-weight: 600; }
        .message p { margin: 8px 0; }
        .message code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>🤖 Markdown聊天测试</h1>
        <p><strong>版本：</strong>2025.08.04 - 纯Markdown解析版本</p>
        
        <div id="messages" class="messages">
            <div class="message assistant">
                <strong>AI助手：</strong>你好！我现在支持Markdown格式渲染。试试发送包含 <code>**粗体**</code>、<code># 标题</code> 或 <code>- 列表</code> 的消息吧！
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="输入消息... (试试：请用markdown格式介绍**人工智能**)" />
            <button onclick="sendMessage()" id="sendBtn">发送</button>
        </div>
        
        <div id="status" class="status">准备就绪 - marked库状态: <span id="markedStatus">检查中...</span></div>
    </div>

    <script>
        const agentId = 7;
        let isLoading = false;

        // 检查marked库状态
        window.addEventListener('load', function() {
            const statusSpan = document.getElementById('markedStatus');
            if (typeof marked !== 'undefined' && marked.parse) {
                statusSpan.textContent = '✅ 已加载';
                statusSpan.style.color = 'green';
                
                // 测试marked库
                try {
                    const testResult = marked.parse('**测试**');
                    console.log('✅ marked库测试成功:', testResult);
                } catch (e) {
                    console.error('❌ marked库测试失败:', e);
                    statusSpan.textContent = '❌ 测试失败';
                    statusSpan.style.color = 'red';
                }
            } else {
                statusSpan.textContent = '❌ 未加载';
                statusSpan.style.color = 'red';
            }
        });

        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isLoading) return;
            
            isLoading = true;
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            
            // 添加AI消息占位符
            const assistantDiv = addMessage('assistant', '', true);
            
            try {
                await streamMessage(message, assistantDiv);
            } catch (error) {
                console.error('❌ 发送失败:', error);
                assistantDiv.innerHTML = '<strong>AI助手：</strong>❌ 发送失败: ' + error.message;
                assistantDiv.classList.remove('streaming');
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
                input.focus();
            }
        }

        // 流式消息处理
        async function streamMessage(message, assistantDiv) {
            updateStatus('🚀 发送请求...');
            
            const response = await fetch(`/v1/agents/${agentId}/execute/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify({
                    query: message,
                    userId: 1,
                    stream: true
                })
            });

            updateStatus(`📡 收到响应: ${response.status}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullContent = '';

            updateStatus('📥 开始接收数据...');

            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    updateStatus('✅ 数据接收完成');
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                console.log('📦 收到数据块:', chunk);
                
                const lines = chunk.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        const data = line.slice(5);
                        console.log('📄 提取数据:', data);
                        
                        if (data === '[DONE]') {
                            console.log('🏁 检测到结束标记');
                            assistantDiv.classList.remove('streaming');
                            updateStatus('🎉 对话完成');
                            return;
                        }
                        
                        if (data && data.trim() !== '') {
                            fullContent += data;
                            updateAssistantMessage(assistantDiv, fullContent);
                            updateStatus(`📝 内容长度: ${fullContent.length}`);
                        }
                    }
                }
            }
        }

        // 更新AI消息内容
        function updateAssistantMessage(div, content) {
            if (typeof marked !== 'undefined' && marked.parse) {
                try {
                    const rendered = marked.parse(content);
                    div.innerHTML = '<strong>AI助手：</strong>' + rendered;
                    console.log('✅ Markdown渲染成功');
                } catch (error) {
                    console.error('❌ Markdown渲染失败:', error);
                    div.innerHTML = '<strong>AI助手：</strong>' + content;
                }
            } else {
                console.warn('⚠️ marked库不可用，使用纯文本');
                div.innerHTML = '<strong>AI助手：</strong>' + content;
            }
            
            // 滚动到底部
            const messages = document.getElementById('messages');
            messages.scrollTop = messages.scrollHeight;
        }

        // 添加消息
        function addMessage(role, content, streaming = false) {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = `message ${role}${streaming ? ' streaming' : ''}`;
            
            if (role === 'user') {
                div.innerHTML = `<strong>用户：</strong>${content}`;
            } else {
                div.innerHTML = `<strong>AI助手：</strong>${content}`;
            }
            
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
            
            return div;
        }

        // 更新状态
        function updateStatus(text) {
            document.getElementById('status').innerHTML = text;
            console.log('📊', text);
        }

        // 回车发送
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !isLoading) {
                sendMessage();
            }
        });
    </script>
</body>
</html>
