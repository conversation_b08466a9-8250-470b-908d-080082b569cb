<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .output {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: monospace;
            margin: 10px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .chat-message {
            background: white;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .streaming {
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>简单调试页面</h1>
    
    <button onclick="testSimple()">测试简单请求</button>
    <button onclick="clearAll()">清空所有</button>
    
    <h3>控制台日志：</h3>
    <div id="console-output" class="output">点击测试按钮开始...</div>
    
    <h3>聊天显示：</h3>
    <div id="chat-display"></div>

    <script>
        function log(message) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        async function testSimple() {
            log('开始测试...');
            
            // 清空之前的聊天显示
            const chatDisplay = document.getElementById('chat-display');
            chatDisplay.innerHTML = '';
            
            // 添加用户消息
            const userMsg = document.createElement('div');
            userMsg.className = 'chat-message';
            userMsg.textContent = '用户: 你好，请简单介绍一下自己';
            chatDisplay.appendChild(userMsg);
            
            // 添加AI消息容器
            const aiMsg = document.createElement('div');
            aiMsg.className = 'chat-message streaming';
            aiMsg.textContent = 'AI: ';
            chatDisplay.appendChild(aiMsg);
            
            let fullContent = '';
            
            try {
                log('发送请求到 /v1/agents/7/execute/stream');
                
                const response = await fetch('/v1/agents/7/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: '你好，请简单介绍一下自己',
                        userId: 1,
                        stream: true
                    })
                });

                log(`HTTP状态: ${response.status} ${response.statusText}`);
                log(`Content-Type: ${response.headers.get('content-type')}`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunkCount = 0;

                log('开始读取流数据...');

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) {
                        log('流读取完成');
                        break;
                    }

                    chunkCount++;
                    const chunk = decoder.decode(value, { stream: true });
                    log(`收到数据块 ${chunkCount}: ${JSON.stringify(chunk)}`);
                    
                    const lines = chunk.split('\n');
                    log(`分割成 ${lines.length} 行`);
                    
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i];
                        log(`处理行 ${i}: ${JSON.stringify(line)}`);
                        
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            log(`提取数据: ${JSON.stringify(data)}`);
                            
                            if (data === '[DONE]') {
                                log('检测到结束标记');
                                aiMsg.classList.remove('streaming');
                                aiMsg.textContent = 'AI: ' + fullContent;
                                return;
                            }
                            
                            if (data && data.trim() !== '') {
                                fullContent += data;
                                aiMsg.textContent = 'AI: ' + fullContent;
                                log(`更新显示内容，当前长度: ${fullContent.length}`);
                            }
                        }
                    }
                }
                
                // 最终更新
                aiMsg.classList.remove('streaming');
                aiMsg.textContent = 'AI: ' + fullContent;
                log(`最终内容长度: ${fullContent.length}`);
                
            } catch (error) {
                log(`错误: ${error.message}`);
                aiMsg.classList.remove('streaming');
                aiMsg.textContent = 'AI: ❌ 错误: ' + error.message;
                aiMsg.style.backgroundColor = '#ffebee';
            }
        }

        function clearAll() {
            document.getElementById('console-output').textContent = '已清空...\n';
            document.getElementById('chat-display').innerHTML = '';
        }
    </script>
</body>
</html>
