<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP配置回显测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 MCP配置回显测试</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试Agent的MCP Server配置是否能正确回显。</p>
            <p><strong>测试Agent ID:</strong> 7</p>
            <p><strong>预期结果:</strong> 应该能看到"本地MCP测试服务"的配置信息</p>
        </div>

        <div class="test-section">
            <h3>🔍 1. 检查Agent基本信息</h3>
            <button onclick="testAgentInfo()">获取Agent信息</button>
            <div id="agentResult"></div>
        </div>

        <div class="test-section">
            <h3>🔌 2. 检查MCP Server配置</h3>
            <button onclick="testMcpConfig()">获取MCP配置</button>
            <div id="mcpResult"></div>
        </div>

        <div class="test-section">
            <h3>🧪 3. 模拟前端加载过程</h3>
            <button onclick="simulatePageLoad()">模拟页面加载</button>
            <div id="simulateResult"></div>
        </div>

        <div class="test-section">
            <h3>🔗 4. 直接访问Agent编辑页面</h3>
            <button onclick="openAgentForm()">打开Agent编辑页面</button>
            <p>点击后会在新窗口打开Agent编辑页面，请检查MCP Server配置是否正确显示。</p>
        </div>
    </div>

    <script>
        // 显示结果
        function showResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${isError ? 'error' : ''}">${JSON.stringify(result, null, 2)}</div>`;
        }

        // 测试Agent信息
        async function testAgentInfo() {
            try {
                const response = await fetch('/v1/agents/7');
                if (response.ok) {
                    const agent = await response.json();
                    showResult('agentResult', {
                        success: true,
                        agent: {
                            id: agent.id,
                            name: agent.name,
                            description: agent.description,
                            mode: agent.mode,
                            status: agent.status
                        }
                    });
                } else {
                    showResult('agentResult', {
                        success: false,
                        error: `HTTP ${response.status}: ${response.statusText}`
                    }, true);
                }
            } catch (error) {
                showResult('agentResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 测试MCP配置
        async function testMcpConfig() {
            try {
                const response = await fetch('/v1/mcp-servers/agent/7');
                if (response.ok) {
                    const mcpServers = await response.json();
                    showResult('mcpResult', {
                        success: true,
                        count: mcpServers.length,
                        mcpServers: mcpServers.map(ams => ({
                            id: ams.id,
                            agentId: ams.agentId,
                            mcpServerId: ams.mcpServerId,
                            enabled: ams.enabled,
                            priority: ams.priority,
                            mcpServer: {
                                id: ams.mcpServer.id,
                                name: ams.mcpServer.name,
                                type: ams.mcpServer.type,
                                description: ams.mcpServer.description,
                                connectionConfig: JSON.parse(ams.mcpServer.connectionConfig || '{}'),
                                status: ams.mcpServer.status,
                                enabled: ams.mcpServer.enabled
                            }
                        }))
                    });
                } else {
                    showResult('mcpResult', {
                        success: false,
                        error: `HTTP ${response.status}: ${response.statusText}`
                    }, true);
                }
            } catch (error) {
                showResult('mcpResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 模拟页面加载过程
        async function simulatePageLoad() {
            try {
                showResult('simulateResult', { message: '开始模拟页面加载过程...' });
                
                // 1. 获取Agent信息
                const agentResponse = await fetch('/v1/agents/7');
                if (!agentResponse.ok) {
                    throw new Error(`获取Agent失败: ${agentResponse.statusText}`);
                }
                const agent = await agentResponse.json();
                
                // 2. 获取MCP配置
                const mcpResponse = await fetch('/v1/mcp-servers/agent/7');
                if (!mcpResponse.ok) {
                    throw new Error(`获取MCP配置失败: ${mcpResponse.statusText}`);
                }
                const mcpServers = await mcpResponse.json();
                
                // 3. 模拟前端处理逻辑
                const processedData = mcpServers.map(agentMcpServer => ({
                    id: agentMcpServer.mcpServerId,
                    name: agentMcpServer.mcpServer.name,
                    type: agentMcpServer.mcpServer.type,
                    description: agentMcpServer.mcpServer.description,
                    priority: agentMcpServer.priority,
                    connectionConfig: agentMcpServer.mcpServer.connectionConfig,
                    status: agentMcpServer.mcpServer.status,
                    enabled: agentMcpServer.enabled
                }));
                
                showResult('simulateResult', {
                    success: true,
                    message: '页面加载模拟完成',
                    agent: {
                        id: agent.id,
                        name: agent.name,
                        mode: agent.mode
                    },
                    mcpServers: processedData,
                    summary: {
                        agentLoaded: true,
                        mcpServersCount: processedData.length,
                        shouldShowMcpConfig: processedData.length > 0
                    }
                });
                
            } catch (error) {
                showResult('simulateResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 打开Agent编辑页面
        function openAgentForm() {
            window.open('/agent-form.html?id=7', '_blank');
        }

        // 页面加载时自动运行基本测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MCP配置回显测试页面加载完成');
            testAgentInfo();
            testMcpConfig();
        });
    </script>
</body>
</html>
