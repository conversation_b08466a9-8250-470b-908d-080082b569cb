<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP服务器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        .server-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 MCP服务器测试工具</h1>
        
        <div class="server-info">
            <h3>📡 服务器信息</h3>
            <p><strong>地址:</strong> http://localhost:8081/mcp</p>
            <p><strong>协议:</strong> JSON-RPC 2.0 (MCP)</p>
            <p><strong>状态:</strong> <span id="serverStatus">未知</span></p>
        </div>

        <div class="test-section">
            <h3>🔧 服务器初始化</h3>
            <button onclick="testInitialize()">初始化连接</button>
            <div id="initResult"></div>
        </div>

        <div class="test-section">
            <h3>🛠️ 获取工具列表</h3>
            <button onclick="testToolsList()">获取可用工具</button>
            <div id="toolsResult"></div>
        </div>

        <div class="test-section">
            <h3>🧮 数学计算测试</h3>
            <div class="input-group">
                <label>数字A:</label>
                <input type="number" id="mathA" value="10">
            </div>
            <div class="input-group">
                <label>数字B:</label>
                <input type="number" id="mathB" value="20">
            </div>
            <button onclick="testMath('add')">加法</button>
            <button onclick="testMath('subtract')">减法</button>
            <button onclick="testMath('multiply')">乘法</button>
            <button onclick="testMath('divide')">除法</button>
            <button onclick="testMath('power')">幂运算</button>
            <div id="mathResult"></div>
        </div>

        <div class="test-section">
            <h3>🌤️ 天气查询测试</h3>
            <div class="input-group">
                <label>城市:</label>
                <input type="text" id="cityName" value="北京">
            </div>
            <button onclick="testWeather()">查询天气</button>
            <button onclick="testWeatherForecast()">天气预报</button>
            <button onclick="testSupportedCities()">支持的城市</button>
            <div id="weatherResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 统计信息</h3>
            <button onclick="testStats('calculation')">计算统计</button>
            <button onclick="testStats('weather')">天气统计</button>
            <div id="statsResult"></div>
        </div>
    </div>

    <script>
        const MCP_URL = 'http://localhost:8081/mcp';
        let requestId = 1;

        // 发送MCP请求
        async function sendMcpRequest(method, params = {}) {
            const request = {
                jsonrpc: "2.0",
                id: requestId++,
                method: method,
                params: params
            };

            try {
                const response = await fetch(MCP_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(request)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                return result;
            } catch (error) {
                throw new Error(`网络错误: ${error.message}`);
            }
        }

        // 显示结果
        function showResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${isError ? 'error' : ''}">${JSON.stringify(result, null, 2)}</div>`;
        }

        // 测试初始化
        async function testInitialize() {
            try {
                const result = await sendMcpRequest('initialize', {
                    protocolVersion: "2024-11-05",
                    capabilities: {},
                    clientInfo: {
                        name: "test-client",
                        version: "1.0.0"
                    }
                });
                
                showResult('initResult', result);
                
                if (result.result) {
                    document.getElementById('serverStatus').textContent = '✅ 已连接';
                    document.getElementById('serverStatus').style.color = 'green';
                }
            } catch (error) {
                showResult('initResult', { error: error.message }, true);
                document.getElementById('serverStatus').textContent = '❌ 连接失败';
                document.getElementById('serverStatus').style.color = 'red';
            }
        }

        // 测试工具列表
        async function testToolsList() {
            try {
                const result = await sendMcpRequest('tools/list');
                showResult('toolsResult', result);
            } catch (error) {
                showResult('toolsResult', { error: error.message }, true);
            }
        }

        // 测试数学计算
        async function testMath(operation) {
            const a = parseFloat(document.getElementById('mathA').value);
            const b = parseFloat(document.getElementById('mathB').value);
            
            let methodName;
            let params;
            
            switch (operation) {
                case 'power':
                    methodName = 'power';
                    params = { base: a, exponent: b };
                    break;
                default:
                    methodName = operation;
                    params = { a: a, b: b };
            }

            try {
                const result = await sendMcpRequest('tools/call', {
                    name: methodName,
                    arguments: params
                });
                showResult('mathResult', result);
            } catch (error) {
                showResult('mathResult', { error: error.message }, true);
            }
        }

        // 测试天气查询
        async function testWeather() {
            const city = document.getElementById('cityName').value;
            
            try {
                const result = await sendMcpRequest('tools/call', {
                    name: 'get_weather',
                    arguments: { city: city }
                });
                showResult('weatherResult', result);
            } catch (error) {
                showResult('weatherResult', { error: error.message }, true);
            }
        }

        // 测试天气预报
        async function testWeatherForecast() {
            const city = document.getElementById('cityName').value;
            
            try {
                const result = await sendMcpRequest('tools/call', {
                    name: 'get_weather_forecast',
                    arguments: { city: city }
                });
                showResult('weatherResult', result);
            } catch (error) {
                showResult('weatherResult', { error: error.message }, true);
            }
        }

        // 测试支持的城市
        async function testSupportedCities() {
            try {
                const result = await sendMcpRequest('tools/call', {
                    name: 'get_supported_cities',
                    arguments: {}
                });
                showResult('weatherResult', result);
            } catch (error) {
                showResult('weatherResult', { error: error.message }, true);
            }
        }

        // 测试统计信息
        async function testStats(type) {
            const methodName = type === 'calculation' ? 'get_calculation_stats' : 'get_weather_stats';
            
            try {
                const result = await sendMcpRequest('tools/call', {
                    name: methodName,
                    arguments: {}
                });
                showResult('statsResult', result);
            } catch (error) {
                showResult('statsResult', { error: error.message }, true);
            }
        }

        // 页面加载时自动测试连接
        document.addEventListener('DOMContentLoaded', function() {
            testInitialize();
        });
    </script>
</body>
</html>
