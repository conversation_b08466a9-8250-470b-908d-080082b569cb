<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }

        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 40px;
        }

        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-box input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 300px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .btn-success {
            background: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        .btn-danger {
            background: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 24px;
        }

        @media (min-width: 1400px) {
            .agent-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (min-width: 1800px) {
            .agent-grid {
                grid-template-columns: repeat(5, 1fr);
            }
        }

        .agent-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: 1px solid #f0f0f0;
        }

        .agent-card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .agent-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .agent-title {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 5px;
        }

        .agent-id {
            font-size: 12px;
            color: #999;
        }

        .agent-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-inactive {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .agent-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .agent-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
        }

        .agent-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .empty {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.3;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 30px;
            padding: 20px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .page-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .page-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>Agent管理中心</h1>
            <div class="header-actions">
                <span id="agentCount">加载中...</span>
                <button class="btn btn-secondary" onclick="window.location.href='index.html'">返回聊天页面</button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="toolbar">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索Agent名称或描述..." onkeyup="handleSearch()">
                <button class="btn btn-secondary" onclick="handleSearch()">搜索</button>
                <button class="btn btn-secondary" onclick="resetSearch()">重置</button>
            </div>
            <div>
                <button class="btn btn-primary" onclick="createAgent()">+ 创建Agent</button>
            </div>
        </div>

        <div id="agentGrid" class="agent-grid">
            <div class="loading">正在加载Agent列表...</div>
        </div>

        <div id="pagination" class="pagination" style="display: none;">
            <!-- 分页控件将在这里动态生成 -->
        </div>
    </div>

    <script>
        let currentPage = 1;
        let pageSize = 12;
        let totalAgents = 0;
        let allAgents = [];
        let filteredAgents = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadAgents();
        });

        // 加载Agent列表
        async function loadAgents() {
            try {
                // 使用固定的用户ID 1，实际项目中应该从登录状态获取
                const response = await fetch('/v1/agents?userId=1');
                if (response.ok) {
                    allAgents = await response.json();
                    // 转换数据格式以适配前端显示
                    allAgents = allAgents.map(agent => ({
                        ...agent,
                        isActive: agent.status === 'PUBLISHED', // 将status转换为isActive
                        mode: agent.mode || 'FUNCTION_CALLING' // 确保有默认模式
                    }));
                    filteredAgents = [...allAgents];
                    totalAgents = allAgents.length;
                    updateAgentCount();
                    displayAgents();
                    updatePagination();
                } else {
                    const errorText = await response.text();
                    showError(`加载Agent列表失败: ${errorText}`);
                }
            } catch (error) {
                console.error('加载Agent列表失败:', error);
                showError('网络错误，请稍后重试');
            }
        }

        // 显示Agent列表
        function displayAgents() {
            const grid = document.getElementById('agentGrid');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageAgents = filteredAgents.slice(startIndex, endIndex);

            if (pageAgents.length === 0) {
                grid.innerHTML = `
                    <div class="empty">
                        <div class="empty-icon">🤖</div>
                        <h3>暂无Agent</h3>
                        <p>点击"创建Agent"开始创建你的第一个AI助手</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = pageAgents.map(agent => `
                <div class="agent-card">
                    <div class="agent-header">
                        <div>
                            <div class="agent-title">${agent.name || '未命名Agent'}</div>
                            <div class="agent-id">ID: ${agent.id}</div>
                        </div>
                        <div class="agent-status ${getStatusClass(agent.status)}">
                            ${getStatusName(agent.status)}
                        </div>
                    </div>
                    <div class="agent-description">
                        ${agent.description || '暂无描述'}
                    </div>
                    <div class="agent-meta">
                        <span>模式: ${getModeName(agent.mode)}</span>
                        <span>创建时间: ${formatDate(agent.createdAt)}</span>
                    </div>
                    <div class="agent-actions">
                        <button class="btn btn-success btn-small" onclick="launchAgent(${agent.id})" ${agent.status !== 'PUBLISHED' ? 'disabled' : ''}>
                            🚀 启动对话
                        </button>
                        ${agent.status === 'DRAFT' ?
                            `<button class="btn btn-primary btn-small" onclick="publishAgent(${agent.id})">📤 发布</button>` :
                            agent.status === 'PUBLISHED' ?
                            `<button class="btn btn-secondary btn-small" onclick="archiveAgent(${agent.id})">📦 归档</button>` :
                            `<button class="btn btn-primary btn-small" onclick="publishAgent(${agent.id})">📤 重新发布</button>`
                        }
                        <button class="btn btn-secondary btn-small" onclick="editAgent(${agent.id})">
                            ✏️ 编辑
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteAgent(${agent.id})">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 更新Agent数量显示
        function updateAgentCount() {
            document.getElementById('agentCount').textContent = 
                `共 ${totalAgents} 个Agent，当前显示 ${filteredAgents.length} 个`;
        }

        // 更新分页
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredAgents.length / pageSize);
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';
            pagination.innerHTML = `
                <button class="page-btn" onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    上一页
                </button>
                ${generatePageNumbers(totalPages)}
                <button class="page-btn" onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    下一页
                </button>
            `;
        }

        // 生成页码
        function generatePageNumbers(totalPages) {
            let pages = [];
            for (let i = 1; i <= totalPages; i++) {
                if (i === currentPage) {
                    pages.push(`<button class="page-btn active">${i}</button>`);
                } else {
                    pages.push(`<button class="page-btn" onclick="goToPage(${i})">${i}</button>`);
                }
            }
            return pages.join('');
        }

        // 跳转页面
        function goToPage(page) {
            const totalPages = Math.ceil(filteredAgents.length / pageSize);
            if (page < 1 || page > totalPages) return;
            
            currentPage = page;
            displayAgents();
            updatePagination();
        }

        // 搜索处理
        function handleSearch() {
            const keyword = document.getElementById('searchInput').value.toLowerCase().trim();
            
            if (!keyword) {
                filteredAgents = [...allAgents];
            } else {
                filteredAgents = allAgents.filter(agent => 
                    (agent.name && agent.name.toLowerCase().includes(keyword)) ||
                    (agent.description && agent.description.toLowerCase().includes(keyword))
                );
            }
            
            currentPage = 1;
            updateAgentCount();
            displayAgents();
            updatePagination();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchInput').value = '';
            filteredAgents = [...allAgents];
            currentPage = 1;
            updateAgentCount();
            displayAgents();
            updatePagination();
        }

        // 创建Agent
        function createAgent() {
            window.location.href = 'agent-form.html';
        }

        // 编辑Agent
        function editAgent(agentId) {
            window.location.href = `agent-form.html?id=${agentId}`;
        }

        // 启动Agent对话
        function launchAgent(agentId) {
            window.open(`chat-fullscreen.html?agentId=${agentId}`, '_blank');
        }

        // 发布Agent
        async function publishAgent(agentId) {
            if (!confirm('确定要发布这个Agent吗？发布后用户就可以使用了。')) {
                return;
            }

            try {
                const response = await fetch(`/v1/agents/${agentId}/publish`, {
                    method: 'POST'
                });

                if (response.ok) {
                    alert('Agent发布成功');
                    loadAgents(); // 重新加载列表
                } else {
                    const errorText = await response.text();
                    alert(`发布失败: ${errorText}`);
                }
            } catch (error) {
                console.error('发布Agent失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 归档Agent
        async function archiveAgent(agentId) {
            if (!confirm('确定要归档这个Agent吗？归档后用户将无法使用。')) {
                return;
            }

            try {
                const response = await fetch(`/v1/agents/${agentId}/archive`, {
                    method: 'POST'
                });

                if (response.ok) {
                    alert('Agent归档成功');
                    loadAgents(); // 重新加载列表
                } else {
                    const errorText = await response.text();
                    alert(`归档失败: ${errorText}`);
                }
            } catch (error) {
                console.error('归档Agent失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 删除Agent
        async function deleteAgent(agentId) {
            if (!confirm('确定要删除这个Agent吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await fetch(`/v1/agents/${agentId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    alert('Agent删除成功');
                    loadAgents(); // 重新加载列表
                } else {
                    const errorText = await response.text();
                    alert(`删除失败: ${errorText}`);
                }
            } catch (error) {
                console.error('删除Agent失败:', error);
                alert('网络错误，请稍后重试');
            }
        }

        // 工具函数
        function getModeName(mode) {
            const modeMap = {
                'FUNCTION_CALLING': '函数调用',
                'REACT': 'ReAct模式',
                'SIMPLE_CHAT': '简单对话',
                'RAG': '知识问答'
            };
            return modeMap[mode] || mode;
        }

        function getStatusName(status) {
            const statusMap = {
                'DRAFT': '草稿',
                'PUBLISHED': '已发布',
                'ARCHIVED': '已归档'
            };
            return statusMap[status] || status;
        }

        function getStatusClass(status) {
            const classMap = {
                'DRAFT': 'status-inactive',
                'PUBLISHED': 'status-active',
                'ARCHIVED': 'status-inactive'
            };
            return classMap[status] || 'status-inactive';
        }

        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        function showError(message) {
            document.getElementById('agentGrid').innerHTML = `
                <div class="empty">
                    <div class="empty-icon">❌</div>
                    <h3>加载失败</h3>
                    <p>${message}</p>
                    <button class="btn btn-primary" onclick="loadAgents()">重新加载</button>
                </div>
            `;
        }

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    </script>
</body>
</html>
