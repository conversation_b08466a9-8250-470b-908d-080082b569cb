<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }

        .breadcrumb {
            color: #999;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #1890ff;
            text-decoration: none;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
        }

        .form-container {
            background: white;
            border-radius: 8px;
            padding: 40px 60px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-label.required::after {
            content: ' *';
            color: #ff4d4f;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .form-help {
            margin-top: 4px;
            font-size: 12px;
            color: #999;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #f0f0f0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-primary:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-danger {
            background: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 16px;
            height: 16px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .form-section {
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-icon {
            font-size: 18px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
        }

        @media (max-width: 1024px) {
            .form-row-3 {
                grid-template-columns: 1fr 1fr;
            }
        }

        /* MCP Server样式 */
        .mcp-server-list {
            margin-top: 16px;
        }

        .mcp-server-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            background: #fafafa;
            position: relative;
        }

        .mcp-server-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .mcp-server-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .mcp-server-actions {
            display: flex;
            gap: 8px;
        }

        .mcp-server-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .mcp-server-form .form-group {
            margin-bottom: 0;
        }

        .mcp-connection-config {
            grid-column: 1 / -1;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-text {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-help {
            font-size: 14px;
            color: #999;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-inactive {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-error {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        @media (max-width: 768px) {
            .form-row,
            .form-row-3 {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 0 20px;
            }

            .form-container {
                padding: 30px;
            }

            .mcp-server-form {
                grid-template-columns: 1fr;
            }

            .mcp-server-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .mcp-server-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 id="pageTitle">创建Agent</h1>
            <div class="breadcrumb">
                <a href="agent-management.html">Agent管理</a> / <span id="breadcrumbTitle">创建Agent</span>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="form-container">
            <div id="message"></div>
            
            <form id="agentForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <span class="section-icon">🤖</span>
                        基本信息
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required" for="name">Agent名称</label>
                        <input type="text" id="name" name="name" class="form-input" required maxlength="100">
                        <div class="form-help">为你的AI助手起一个有意义的名称</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="description">描述</label>
                        <textarea id="description" name="description" class="form-input form-textarea" maxlength="500"></textarea>
                        <div class="form-help">简要描述这个Agent的功能和用途</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required" for="mode">工作模式</label>
                            <select id="mode" name="mode" class="form-select" required>
                                <option value="">请选择工作模式</option>
                                <option value="FUNCTION_CALLING">函数调用模式</option>
                                <option value="REACT">ReAct模式</option>
                            </select>
                            <div class="form-help">选择Agent的工作方式</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="status">状态</label>
                            <select id="status" name="status" class="form-select">
                                <option value="DRAFT">草稿</option>
                                <option value="PUBLISHED">已发布</option>
                                <option value="ARCHIVED">已归档</option>
                            </select>
                            <div class="form-help">只有已发布的Agent才能使用</div>
                        </div>
                    </div>
                </div>

                <!-- 系统提示词 -->
                <div class="form-section">
                    <div class="section-title">
                        <span class="section-icon">💬</span>
                        系统提示词
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required" for="systemPrompt">系统提示词</label>
                        <textarea id="systemPrompt" name="systemPrompt" class="form-input form-textarea" 
                                  style="min-height: 200px;" required maxlength="2000"></textarea>
                        <div class="form-help">定义Agent的角色、行为和回答风格。这是Agent的核心配置。</div>
                    </div>
                </div>

                <!-- 高级配置 -->
                <div class="form-section">
                    <div class="section-title">
                        <span class="section-icon">⚙️</span>
                        高级配置
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="temperature">创造性 (Temperature)</label>
                            <input type="number" id="temperature" name="temperature" class="form-input" 
                                   min="0" max="2" step="0.1" value="0.7">
                            <div class="form-help">0-2之间，越高越有创造性</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="maxTokens">最大回复长度</label>
                            <input type="number" id="maxTokens" name="maxTokens" class="form-input" 
                                   min="100" max="4000" step="100" value="1000">
                            <div class="form-help">单次回复的最大token数量</div>
                        </div>
                    </div>
                </div>

                <!-- MCP Server配置 -->
                <div class="form-section">
                    <div class="section-title">
                        <span class="section-icon">🔌</span>
                        MCP Server配置
                        <button type="button" class="btn btn-small btn-primary" onclick="addMcpServer()" style="margin-left: auto;">
                            ➕ 添加MCP Server
                        </button>
                    </div>

                    <div id="mcpServerList" class="mcp-server-list">
                        <div class="empty-state" id="mcpEmptyState">
                            <div class="empty-icon">🔌</div>
                            <div class="empty-text">暂无MCP Server配置</div>
                            <div class="empty-help">MCP Server可以为Agent提供额外的工具和能力</div>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="goBack()">取消</button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <span id="submitText">创建Agent</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let isEditMode = false;
        let currentAgentId = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const agentId = urlParams.get('id');
            
            if (agentId) {
                isEditMode = true;
                currentAgentId = agentId;
                updatePageForEditMode();
                loadAgentData(agentId);
            }
        });

        // 更新页面为编辑模式
        function updatePageForEditMode() {
            document.getElementById('pageTitle').textContent = '编辑Agent';
            document.getElementById('breadcrumbTitle').textContent = '编辑Agent';
            document.getElementById('submitText').textContent = '保存修改';
        }

        // 加载Agent数据
        async function loadAgentData(agentId) {
            try {
                showMessage('正在加载Agent数据...', 'info');
                
                const response = await fetch(`/v1/agents/${agentId}`);
                if (response.ok) {
                    const agent = await response.json();
                    populateForm(agent);
                    clearMessage();
                } else {
                    showMessage('加载Agent数据失败', 'error');
                }
            } catch (error) {
                console.error('加载Agent数据失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            }
        }

        // 填充表单数据
        function populateForm(agent) {
            document.getElementById('name').value = agent.name || '';
            document.getElementById('description').value = agent.description || '';
            document.getElementById('mode').value = agent.mode || '';
            document.getElementById('systemPrompt').value = agent.systemPrompt || '';
            document.getElementById('status').value = agent.status || 'DRAFT';

            // 处理模型配置
            if (agent.modelConfig) {
                document.getElementById('temperature').value = agent.modelConfig.temperature || 0.7;
                document.getElementById('maxTokens').value = agent.modelConfig.maxTokens || 1000;
            } else {
                document.getElementById('temperature').value = 0.7;
                document.getElementById('maxTokens').value = 1000;
            }

            // 加载MCP Server配置
            if (isEditMode && currentAgentId) {
                loadAgentMcpServers(currentAgentId);
            }
        }

        // 加载Agent的MCP Server配置
        async function loadAgentMcpServers(agentId) {
            try {
                const response = await fetch(`/v1/mcp-servers/agent/${agentId}`);
                if (response.ok) {
                    const agentMcpServers = await response.json();

                    console.log('加载到的MCP Server配置:', agentMcpServers);

                    // 清空现有的MCP Server配置
                    mcpServers.length = 0;
                    document.getElementById('mcpServerList').innerHTML = `
                        <div class="empty-state" id="mcpEmptyState">
                            <div class="empty-icon">🔌</div>
                            <div class="empty-text">暂无MCP Server配置</div>
                            <div class="empty-help">MCP Server可以为Agent提供额外的工具和能力</div>
                        </div>
                    `;

                    // 添加每个MCP Server配置
                    for (const agentMcpServer of agentMcpServers) {
                        const serverData = {
                            id: agentMcpServer.mcpServerId,
                            name: agentMcpServer.mcpServer.name,
                            type: agentMcpServer.mcpServer.type,
                            description: agentMcpServer.mcpServer.description,
                            priority: agentMcpServer.priority,
                            connectionConfig: agentMcpServer.mcpServer.connectionConfig,
                            status: agentMcpServer.mcpServer.status,
                            enabled: agentMcpServer.enabled
                        };

                        console.log('准备添加MCP Server:', serverData);
                        addMcpServer(serverData);
                    }

                    console.log('MCP Server配置加载完成，共', agentMcpServers.length, '个');
                } else {
                    console.warn('加载MCP Server配置失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载MCP Server配置异常:', error);
            }
        }

        // 表单提交处理
        document.getElementById('agentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const originalText = submitText.textContent;
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitText.textContent = isEditMode ? '保存中...' : '创建中...';
            
            try {
                const formData = new FormData(this);
                const agentData = {
                    name: formData.get('name'),
                    description: formData.get('description'),
                    mode: formData.get('mode'),
                    systemPrompt: formData.get('systemPrompt'),
                    status: formData.get('status'),
                    creatorId: 1, // 固定用户ID，实际项目中应该从登录状态获取
                    modelConfig: {
                        modelName: 'qwen-turbo',
                        temperature: parseFloat(formData.get('temperature')),
                        maxTokens: parseInt(formData.get('maxTokens')),
                        topP: 1.0,
                        frequencyPenalty: 0.0,
                        presencePenalty: 0.0,
                        streamEnabled: true
                    }
                };

                const url = isEditMode ? `/v1/agents/${currentAgentId}` : '/v1/agents';
                const method = isEditMode ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(agentData)
                });

                if (response.ok) {
                    const result = await response.json();

                    // 保存MCP Server配置
                    if (mcpServers.length > 0) {
                        await saveMcpServerConfigurations(result.id || currentAgentId);
                    }

                    showMessage(
                        isEditMode ? 'Agent修改成功！' : 'Agent创建成功！',
                        'success'
                    );

                    // 2秒后跳转回管理页面
                    setTimeout(() => {
                        window.location.href = 'agent-management.html';
                    }, 2000);
                } else {
                    const error = await response.text();
                    showMessage(`操作失败: ${error}`, 'error');
                }
            } catch (error) {
                console.error('提交失败:', error);
                showMessage('网络错误，请稍后重试', 'error');
            } finally {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitText.textContent = originalText;
            }
        });

        // 显示消息
        function showMessage(text, type = 'info') {
            const messageDiv = document.getElementById('message');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            messageDiv.innerHTML = `<div class="${className}">${text}</div>`;
        }

        // 清除消息
        function clearMessage() {
            document.getElementById('message').innerHTML = '';
        }

        // 返回上一页
        function goBack() {
            if (confirm('确定要离开吗？未保存的修改将丢失。')) {
                window.location.href = 'agent-management.html';
            }
        }

        // 表单验证
        function validateForm() {
            const name = document.getElementById('name').value.trim();
            const mode = document.getElementById('mode').value;
            const systemPrompt = document.getElementById('systemPrompt').value.trim();

            if (!name) {
                showMessage('请输入Agent名称', 'error');
                return false;
            }

            if (!mode) {
                showMessage('请选择工作模式', 'error');
                return false;
            }

            if (!systemPrompt) {
                showMessage('请输入系统提示词', 'error');
                return false;
            }

            return true;
        }

        // 监听表单变化，实时验证
        document.getElementById('agentForm').addEventListener('input', function() {
            clearMessage();
        });

        // MCP Server管理
        let mcpServerCounter = 0;
        let mcpServers = [];

        // 添加MCP Server
        function addMcpServer(serverData = null) {
            mcpServerCounter++;
            const serverId = serverData ? serverData.id : `new_${mcpServerCounter}`;

            console.log('添加MCP Server，serverId:', serverId, 'serverData:', serverData);

            const mcpServerItem = document.createElement('div');
            mcpServerItem.className = 'mcp-server-item';
            mcpServerItem.id = `mcp-server-${serverId}`;

            mcpServerItem.innerHTML = `
                <div class="mcp-server-header">
                    <div class="mcp-server-title">
                        <span>🔌</span>
                        <span>${serverData ? serverData.name : `MCP Server ${mcpServerCounter}`}</span>
                        ${serverData ? `<span class="status-indicator status-${getStatusClass(serverData.status)}">${getStatusName(serverData.status)}</span>` : ''}
                    </div>
                    <div class="mcp-server-actions">
                        <button type="button" class="btn btn-small btn-secondary" onclick="testMcpConnection('${serverId}')">
                            🔍 测试连接
                        </button>
                        <button type="button" class="btn btn-small btn-danger" onclick="removeMcpServer('${serverId}')">
                            🗑️ 删除
                        </button>
                    </div>
                </div>

                <div class="mcp-server-form">
                    <div class="form-group">
                        <label class="form-label required">Server名称</label>
                        <input type="text" class="form-input" name="mcp_name_${serverId}"
                               value="${serverData ? serverData.name : ''}" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Server类型</label>
                        <select class="form-select" name="mcp_type_${serverId}" onchange="updateMcpConfig('${serverId}')" required>
                            <option value="">请选择类型</option>
                            <option value="HTTP" ${serverData && serverData.type === 'HTTP' ? 'selected' : ''}>HTTP/REST API</option>
                            <option value="WEBSOCKET" ${serverData && serverData.type === 'WEBSOCKET' ? 'selected' : ''}>WebSocket</option>
                            <option value="STDIO" ${serverData && serverData.type === 'STDIO' ? 'selected' : ''}>标准输入输出</option>
                            <option value="TCP" ${serverData && serverData.type === 'TCP' ? 'selected' : ''}>TCP连接</option>
                            <option value="CUSTOM" ${serverData && serverData.type === 'CUSTOM' ? 'selected' : ''}>自定义协议</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-input" name="mcp_description_${serverId}"
                               value="${serverData ? serverData.description || '' : ''}">
                    </div>

                    <div class="form-group">
                        <label class="form-label">优先级</label>
                        <input type="number" class="form-input" name="mcp_priority_${serverId}"
                               value="${serverData ? serverData.priority || 0 : 0}" min="0" max="100">
                        <div class="form-help">数字越小优先级越高</div>
                    </div>

                    <div class="form-group mcp-connection-config">
                        <label class="form-label">连接配置</label>
                        <div id="mcp-config-${serverId}">
                            ${generateMcpConfigForm(serverData ? serverData.type : '', serverId, serverData)}
                        </div>
                    </div>
                </div>
            `;

            const mcpServerList = document.getElementById('mcpServerList');
            const emptyState = document.getElementById('mcpEmptyState');

            if (emptyState) {
                emptyState.style.display = 'none';
            }

            mcpServerList.appendChild(mcpServerItem);

            // 添加到数组中
            mcpServers.push({
                id: serverId,
                element: mcpServerItem,
                data: serverData
            });

            console.log('MCP Server添加完成，当前数组长度:', mcpServers.length);
        }

        // 删除MCP Server
        function removeMcpServer(serverId) {
            if (confirm('确定要删除这个MCP Server配置吗？')) {
                const serverItem = document.getElementById(`mcp-server-${serverId}`);
                if (serverItem) {
                    serverItem.remove();
                }

                // 从数组中移除
                mcpServers = mcpServers.filter(server => server.id !== serverId);

                // 如果没有MCP Server了，显示空状态
                if (mcpServers.length === 0) {
                    const emptyState = document.getElementById('mcpEmptyState');
                    if (emptyState) {
                        emptyState.style.display = 'block';
                    }
                }
            }
        }

        // 生成MCP配置表单
        function generateMcpConfigForm(type, serverId, serverData = null) {
            let connectionConfig = {};

            if (serverData && serverData.connectionConfig) {
                try {
                    if (typeof serverData.connectionConfig === 'string') {
                        connectionConfig = JSON.parse(serverData.connectionConfig);
                    } else {
                        connectionConfig = serverData.connectionConfig;
                    }
                } catch (e) {
                    console.error('解析connectionConfig失败:', e, serverData.connectionConfig);
                    connectionConfig = {};
                }
            }

            console.log('生成MCP配置表单:', { type, serverId, connectionConfig });

            switch (type) {
                case 'HTTP':
                    return `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="form-group">
                                <label class="form-label required">Base URL</label>
                                <input type="url" class="form-input" name="mcp_base_url_${serverId}"
                                       value="${connectionConfig.baseUrl || ''}" placeholder="https://api.example.com" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-input" name="mcp_api_key_${serverId}"
                                       value="${connectionConfig.apiKey || ''}" placeholder="可选的API密钥">
                            </div>
                            <div class="form-group">
                                <label class="form-label">超时时间(秒)</label>
                                <input type="number" class="form-input" name="mcp_timeout_${serverId}"
                                       value="${connectionConfig.timeout || 30}" min="1" max="300">
                            </div>
                            <div class="form-group">
                                <label class="form-label">重试次数</label>
                                <input type="number" class="form-input" name="mcp_retries_${serverId}"
                                       value="${connectionConfig.retries || 3}" min="0" max="10">
                            </div>
                        </div>
                    `;
                case 'WEBSOCKET':
                    return `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="form-group">
                                <label class="form-label required">WebSocket URL</label>
                                <input type="url" class="form-input" name="mcp_ws_url_${serverId}"
                                       value="${connectionConfig.wsUrl || ''}" placeholder="ws://localhost:8080/mcp" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">心跳间隔(秒)</label>
                                <input type="number" class="form-input" name="mcp_heartbeat_${serverId}"
                                       value="${connectionConfig.heartbeat || 30}" min="5" max="300">
                            </div>
                        </div>
                    `;
                case 'STDIO':
                    return `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="form-group">
                                <label class="form-label required">执行命令</label>
                                <input type="text" class="form-input" name="mcp_command_${serverId}"
                                       value="${connectionConfig.command || ''}" placeholder="python mcp_server.py" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">工作目录</label>
                                <input type="text" class="form-input" name="mcp_workdir_${serverId}"
                                       value="${connectionConfig.workdir || ''}" placeholder="/path/to/server">
                            </div>
                        </div>
                    `;
                case 'TCP':
                    return `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                            <div class="form-group">
                                <label class="form-label required">主机地址</label>
                                <input type="text" class="form-input" name="mcp_host_${serverId}"
                                       value="${connectionConfig.host || 'localhost'}" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">端口</label>
                                <input type="number" class="form-input" name="mcp_port_${serverId}"
                                       value="${connectionConfig.port || 8080}" min="1" max="65535" required>
                            </div>
                        </div>
                    `;
                default:
                    return `
                        <div class="form-group">
                            <label class="form-label">自定义配置 (JSON)</label>
                            <textarea class="form-input form-textarea" name="mcp_custom_config_${serverId}"
                                      style="min-height: 100px;" placeholder='{"key": "value"}'>${serverData && serverData.connectionConfig ? serverData.connectionConfig : ''}</textarea>
                            <div class="form-help">请输入有效的JSON格式配置</div>
                        </div>
                    `;
            }
        }

        // 更新MCP配置表单
        function updateMcpConfig(serverId) {
            const typeSelect = document.querySelector(`select[name="mcp_type_${serverId}"]`);
            const configContainer = document.getElementById(`mcp-config-${serverId}`);

            if (typeSelect && configContainer) {
                configContainer.innerHTML = generateMcpConfigForm(typeSelect.value, serverId);
            }
        }

        // 测试MCP连接
        async function testMcpConnection(serverId) {
            const button = event.target;
            const originalText = button.innerHTML;

            button.innerHTML = '⏳ 测试中...';
            button.disabled = true;

            try {
                // 收集MCP Server配置
                const mcpConfig = collectMcpServerConfig(serverId);

                // 构建测试用的MCP Server对象
                const testMcpServer = {
                    name: mcpConfig.name,
                    type: mcpConfig.type,
                    connectionConfig: mcpConfig.connectionConfig,
                    enabled: true
                };

                // 调用后端测试API
                const response = await fetch('/v1/mcp-servers/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testMcpServer)
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        alert(`✅ 连接测试成功！\n\nMCP Server: ${mcpConfig.name}\n类型: ${mcpConfig.type}\n状态: 连接正常`);
                    } else {
                        alert(`❌ 连接测试失败\n\n错误信息: ${result.message}`);
                    }
                } else {
                    const error = await response.text();
                    alert(`❌ 连接测试失败\n\n服务器错误: ${error}`);
                }
            } catch (error) {
                alert(`❌ 连接测试异常\n\n错误信息: ${error.message}`);
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // 收集MCP Server配置
        function collectMcpServerConfig(serverId) {
            const form = document.getElementById('agentForm');
            const formData = new FormData(form);

            const config = {
                name: formData.get(`mcp_name_${serverId}`),
                type: formData.get(`mcp_type_${serverId}`),
                description: formData.get(`mcp_description_${serverId}`),
                priority: parseInt(formData.get(`mcp_priority_${serverId}`) || 0)
            };

            // 根据类型收集连接配置
            const connectionConfig = {};
            switch (config.type) {
                case 'HTTP':
                    connectionConfig.baseUrl = formData.get(`mcp_base_url_${serverId}`);
                    connectionConfig.apiKey = formData.get(`mcp_api_key_${serverId}`);
                    connectionConfig.timeout = parseInt(formData.get(`mcp_timeout_${serverId}`) || 30);
                    connectionConfig.retries = parseInt(formData.get(`mcp_retries_${serverId}`) || 3);
                    break;
                case 'WEBSOCKET':
                    connectionConfig.wsUrl = formData.get(`mcp_ws_url_${serverId}`);
                    connectionConfig.heartbeat = parseInt(formData.get(`mcp_heartbeat_${serverId}`) || 30);
                    break;
                case 'STDIO':
                    connectionConfig.command = formData.get(`mcp_command_${serverId}`);
                    connectionConfig.workdir = formData.get(`mcp_workdir_${serverId}`);
                    break;
                case 'TCP':
                    connectionConfig.host = formData.get(`mcp_host_${serverId}`);
                    connectionConfig.port = parseInt(formData.get(`mcp_port_${serverId}`) || 8080);
                    break;
                default:
                    const customConfig = formData.get(`mcp_custom_config_${serverId}`);
                    if (customConfig) {
                        try {
                            Object.assign(connectionConfig, JSON.parse(customConfig));
                        } catch (e) {
                            console.error('Invalid JSON in custom config:', e);
                        }
                    }
            }

            config.connectionConfig = JSON.stringify(connectionConfig);
            return config;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const classMap = {
                'ACTIVE': 'active',
                'INACTIVE': 'inactive',
                'CONNECTING': 'inactive',
                'ERROR': 'error',
                'MAINTENANCE': 'inactive'
            };
            return classMap[status] || 'inactive';
        }

        // 获取状态名称
        function getStatusName(status) {
            const nameMap = {
                'ACTIVE': '活跃',
                'INACTIVE': '非活跃',
                'CONNECTING': '连接中',
                'ERROR': '错误',
                'MAINTENANCE': '维护中'
            };
            return nameMap[status] || status;
        }

        // 保存MCP Server配置
        async function saveMcpServerConfigurations(agentId) {
            try {
                const mcpServerConfigs = [];

                for (const mcpServer of mcpServers) {
                    const config = collectMcpServerConfig(mcpServer.id);

                    // 首先创建或更新MCP Server
                    const mcpServerData = {
                        name: config.name,
                        description: config.description,
                        type: config.type,
                        connectionConfig: config.connectionConfig,
                        status: 'INACTIVE',
                        enabled: true,
                        creatorId: 1 // 固定用户ID，实际项目中应该从登录状态获取
                    };

                    let mcpServerId;
                    if (mcpServer.data && mcpServer.data.id && !mcpServer.id.startsWith('new_')) {
                        // 更新现有MCP Server
                        const updateResponse = await fetch(`/v1/mcp-servers/${mcpServer.data.id}`, {
                            method: 'PUT',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(mcpServerData)
                        });

                        if (updateResponse.ok) {
                            const updated = await updateResponse.json();
                            mcpServerId = updated.id;
                        } else {
                            console.error('更新MCP Server失败:', await updateResponse.text());
                            continue;
                        }
                    } else {
                        // 创建新MCP Server
                        const createResponse = await fetch('/v1/mcp-servers', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(mcpServerData)
                        });

                        if (createResponse.ok) {
                            const created = await createResponse.json();
                            mcpServerId = created.id;
                        } else {
                            console.error('创建MCP Server失败:', await createResponse.text());
                            continue;
                        }
                    }

                    // 添加到Agent关联配置
                    mcpServerConfigs.push({
                        mcpServerId: mcpServerId,
                        enabled: true,
                        priority: config.priority
                    });
                }

                // 保存Agent与MCP Server的关联关系
                if (mcpServerConfigs.length > 0) {
                    const response = await fetch(`/v1/mcp-servers/agent/${agentId}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(mcpServerConfigs)
                    });

                    if (!response.ok) {
                        console.error('保存MCP Server关联失败:', await response.text());
                    }
                }

            } catch (error) {
                console.error('保存MCP Server配置异常:', error);
            }
        }
    </script>
</body>
</html>
