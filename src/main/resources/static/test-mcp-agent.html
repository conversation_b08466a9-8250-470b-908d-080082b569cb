<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Agent测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            width: 200px;
        }
        .test-case {
            background: #f0f8ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-case h4 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 MCP Agent测试工具</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试Agent是否能正确调用MCP Server进行数学计算和天气查询。</p>
            <p><strong>测试Agent ID:</strong> 7 (已配置MCP Server)</p>
            <p><strong>MCP Server:</strong> http://localhost:8081/mcp</p>
        </div>

        <div class="test-section">
            <h3>🔍 1. 验证MCP配置</h3>
            <button onclick="checkMcpConfig()">检查MCP配置</button>
            <div id="configResult"></div>
        </div>

        <div class="test-section">
            <h3>🧮 2. 数学计算测试</h3>
            
            <div class="test-case">
                <h4>测试用例1: 简单加法 (3 + 2)</h4>
                <button onclick="testMathCalculation('帮我计算 3 + 2')">测试加法</button>
                <div id="mathResult1"></div>
            </div>
            
            <div class="test-case">
                <h4>测试用例2: 复杂计算 (10 * 5 + 3)</h4>
                <button onclick="testMathCalculation('请计算 10 乘以 5 再加上 3')">测试复合运算</button>
                <div id="mathResult2"></div>
            </div>
            
            <div class="test-case">
                <h4>测试用例3: 幂运算 (2^8)</h4>
                <button onclick="testMathCalculation('计算 2 的 8 次方')">测试幂运算</button>
                <div id="mathResult3"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌤️ 3. 天气查询测试</h3>
            
            <div class="test-case">
                <h4>测试用例1: 查询北京天气</h4>
                <button onclick="testWeatherQuery('北京今天天气怎么样？')">查询北京天气</button>
                <div id="weatherResult1"></div>
            </div>
            
            <div class="test-case">
                <h4>测试用例2: 查询上海天气预报</h4>
                <button onclick="testWeatherQuery('上海未来三天的天气预报')">查询天气预报</button>
                <div id="weatherResult2"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 4. 自定义测试</h3>
            <div class="input-group">
                <label>测试问题:</label>
                <input type="text" id="customQuery" placeholder="输入你的问题..." style="width: 400px;">
            </div>
            <button onclick="testCustomQuery()">发送测试</button>
            <div id="customResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 5. 直接MCP调用测试</h3>
            <button onclick="testDirectMcpCall()">直接调用MCP Server</button>
            <div id="directResult"></div>
        </div>
    </div>

    <script>
        // 显示结果
        function showResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            const resultText = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
            element.innerHTML = `<div class="result ${isError ? 'error' : ''}">${resultText}</div>`;
        }

        // 检查MCP配置
        async function checkMcpConfig() {
            try {
                const response = await fetch('/v1/mcp-servers/agent/7');
                if (response.ok) {
                    const mcpServers = await response.json();
                    showResult('configResult', {
                        success: true,
                        message: `找到 ${mcpServers.length} 个MCP Server配置`,
                        servers: mcpServers.map(ams => ({
                            name: ams.mcpServer.name,
                            type: ams.mcpServer.type,
                            enabled: ams.enabled,
                            priority: ams.priority,
                            status: ams.mcpServer.status
                        }))
                    });
                } else {
                    showResult('configResult', {
                        success: false,
                        error: `HTTP ${response.status}: ${response.statusText}`
                    }, true);
                }
            } catch (error) {
                showResult('configResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 测试数学计算
        async function testMathCalculation(query) {
            const resultId = query.includes('3 + 2') ? 'mathResult1' : 
                           query.includes('10') ? 'mathResult2' : 'mathResult3';
            
            try {
                showResult(resultId, '🔄 正在调用Agent进行计算...');
                
                const response = await fetch('/v1/agents/7/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        userId: 1,
                        stream: false
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(resultId, {
                        success: true,
                        query: query,
                        response: result.response,
                        finished: result.finished,
                        timestamp: new Date().toLocaleTimeString()
                    });
                } else {
                    const error = await response.text();
                    showResult(resultId, {
                        success: false,
                        error: `HTTP ${response.status}: ${error}`
                    }, true);
                }
            } catch (error) {
                showResult(resultId, {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 测试天气查询
        async function testWeatherQuery(query) {
            const resultId = query.includes('北京') ? 'weatherResult1' : 'weatherResult2';
            
            try {
                showResult(resultId, '🔄 正在调用Agent查询天气...');
                
                const response = await fetch('/v1/agents/7/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        userId: 1,
                        stream: false
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult(resultId, {
                        success: true,
                        query: query,
                        response: result.response,
                        finished: result.finished,
                        timestamp: new Date().toLocaleTimeString()
                    });
                } else {
                    const error = await response.text();
                    showResult(resultId, {
                        success: false,
                        error: `HTTP ${response.status}: ${error}`
                    }, true);
                }
            } catch (error) {
                showResult(resultId, {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 自定义测试
        async function testCustomQuery() {
            const query = document.getElementById('customQuery').value.trim();
            if (!query) {
                showResult('customResult', { error: '请输入测试问题' }, true);
                return;
            }

            try {
                showResult('customResult', '🔄 正在处理自定义查询...');
                
                const response = await fetch('/v1/agents/7/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        userId: 1,
                        stream: false
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showResult('customResult', {
                        success: true,
                        query: query,
                        response: result.response,
                        finished: result.finished,
                        timestamp: new Date().toLocaleTimeString()
                    });
                } else {
                    const error = await response.text();
                    showResult('customResult', {
                        success: false,
                        error: `HTTP ${response.status}: ${error}`
                    }, true);
                }
            } catch (error) {
                showResult('customResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 直接MCP调用测试
        async function testDirectMcpCall() {
            try {
                showResult('directResult', '🔄 正在直接调用MCP Server...');
                
                // 直接调用MCP Server的add工具
                const response = await fetch('/v1/mcp-servers/1/tools/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        a: 3,
                        b: 2
                    })
                });

                if (response.ok) {
                    const result = await response.text();
                    showResult('directResult', {
                        success: true,
                        message: '直接MCP调用成功',
                        mcpResponse: result,
                        timestamp: new Date().toLocaleTimeString()
                    });
                } else {
                    const error = await response.text();
                    showResult('directResult', {
                        success: false,
                        error: `HTTP ${response.status}: ${error}`
                    }, true);
                }
            } catch (error) {
                showResult('directResult', {
                    success: false,
                    error: error.message
                }, true);
            }
        }

        // 页面加载时自动检查配置
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MCP Agent测试页面加载完成');
            checkMcpConfig();
        });
    </script>
</body>
</html>
