<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最小测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        #output { background: #f0f0f0; padding: 10px; margin: 10px 0; min-height: 100px; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>最小测试页面</h1>
    <button onclick="test()">开始测试</button>
    <button onclick="clear()">清空</button>
    <div id="output">点击测试按钮...</div>

    <script>
        async function test() {
            const output = document.getElementById('output');
            output.innerHTML = '开始测试...<br>';
            
            try {
                console.log('发送请求...');
                
                const response = await fetch('/v1/agents/7/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: '你好',
                        userId: 1,
                        stream: true
                    })
                });

                console.log('响应状态:', response.status);
                output.innerHTML += `HTTP状态: ${response.status}<br>`;

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let content = '';

                output.innerHTML += '开始接收数据...<br>';
                console.log('开始读取流...');

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        console.log('流结束');
                        output.innerHTML += '<br>流结束';
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    console.log('收到:', chunk);
                    output.innerHTML += `收到: ${chunk}<br>`;
                    
                    const lines = chunk.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            console.log('数据:', data);
                            
                            if (data === '[DONE]') {
                                console.log('完成');
                                output.innerHTML += '<br><strong>完成!</strong><br>';
                                output.innerHTML += `<br>最终内容: ${content}`;
                                return;
                            }
                            
                            if (data.trim()) {
                                content += data;
                                output.innerHTML += `内容: ${data}<br>`;
                                console.log('累积内容:', content);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('错误:', error);
                output.innerHTML += `<br>错误: ${error.message}`;
            }
        }

        function clear() {
            document.getElementById('output').innerHTML = '已清空...';
        }
    </script>
</body>
</html>
