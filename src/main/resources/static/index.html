<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent V3 - 正在跳转到管理中心...</title>
    <meta http-equiv="refresh" content="0; url=agent-management.html">
    <script>
        // JavaScript重定向作为备用
        window.location.href = 'agent-management.html';
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px 40px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            position: relative;
        }
        
        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 350px 400px 1fr;
            gap: 24px;
            height: calc(100vh - 180px);
        }

        @media (max-width: 1400px) {
            .main-content {
                grid-template-columns: 300px 350px 1fr;
                gap: 20px;
            }
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 280px 320px 1fr;
                gap: 16px;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr 1fr;
                gap: 16px;
                height: auto;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }
        
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .card h2 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .response {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            white-space: pre-wrap;
        }
        
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        
        .loading {
            color: #1890ff;
        }
        
        .agent-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .conversation-list {
            max-height: 150px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        
        .agent-item {
            padding: 10px;
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .agent-item:hover {
            background: #f5f5f5;
        }
        
        .agent-item.selected {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        .conversation-item {
            padding: 8px;
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .conversation-item:hover {
            background: #f5f5f5;
        }

        .conversation-item.selected {
            border-color: #52c41a;
            background: #f6ffed;
        }

        .conversation-title {
            font-weight: 500;
            margin-bottom: 3px;
        }

        .conversation-time {
            color: #999;
            font-size: 11px;
        }
        
        .agent-name {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .agent-mode {
            font-size: 12px;
            color: #666;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 2px;
            display: inline-block;
        }

        /* 聊天界面样式 */
        .chat-container {
            height: 500px;
            display: flex;
            flex-direction: column;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #fafafa;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user {
            align-self: flex-end;
            background: #1890ff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant {
            align-self: flex-start;
            background: white;
            color: #333;
            border: 1px solid #e8e8e8;
            border-bottom-left-radius: 4px;
        }

        .message.system {
            align-self: center;
            background: #f0f0f0;
            color: #666;
            font-size: 12px;
            padding: 8px 12px;
            border-radius: 12px;
        }

        .message.streaming {
            position: relative;
        }

        .message.streaming::after {
            content: '▋';
            animation: blink 1s infinite;
            color: #1890ff;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .system-message {
            text-align: center;
            color: #999;
            font-size: 14px;
            padding: 20px;
        }

        .chat-input-container {
            border-top: 1px solid #e8e8e8;
            background: white;
            padding: 15px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-group textarea {
            flex: 1;
            resize: none;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            padding: 10px 12px;
            font-size: 14px;
            line-height: 1.4;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .input-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .btn-send {
            background: #1890ff;
            min-width: 60px;
        }

        .btn-send:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }

        .btn-clear {
            background: #f5f5f5;
            color: #666;
            font-size: 12px;
            padding: 6px 12px;
        }

        .btn-clear:hover {
            background: #e8e8e8;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
            padding: 8px 16px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #1890ff;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Agent V3 - 智能Agent平台</h1>
            <p>支持ReAct和Function Calling两种执行模式的AI Agent平台</p>
            <div style="position: absolute; top: 20px; right: 20px;">
                <a href="agent-management.html" style="color: #1890ff; text-decoration: none; padding: 8px 16px; border: 1px solid #1890ff; border-radius: 4px; font-size: 14px; transition: all 0.3s;"
                   onmouseover="this.style.background='#1890ff'; this.style.color='white';"
                   onmouseout="this.style.background='transparent'; this.style.color='#1890ff';">
                    🛠️ Agent管理
                </a>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Agent配置 -->
            <div class="card">
                <h2>Agent配置</h2>
                <form id="agentForm">
                    <div class="form-group">
                        <label>Agent名称</label>
                        <input type="text" id="agentName" placeholder="请输入Agent名称" required>
                    </div>
                    
                    <div class="form-group">
                        <label>执行模式</label>
                        <select id="agentMode" required>
                            <option value="FUNCTION_CALLING">Function Calling模式</option>
                            <option value="REACT">ReAct模式</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>系统提示词</label>
                        <textarea id="systemPrompt" placeholder="请输入系统提示词，这将决定Agent的行为和能力" required>你是一个智能助手，可以帮助用户解答问题和执行任务。请友好、准确地回答用户的问题。</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>开场白</label>
                        <textarea id="openingStatement" placeholder="请输入开场白">你好！我是你的AI助手，有什么可以帮助你的吗？</textarea>
                    </div>
                    
                    <button type="submit" class="btn">创建Agent</button>
                    <button type="button" class="btn btn-success" onclick="loadAgents()">刷新列表</button>
                </form>
                
                <div id="createResponse"></div>
            </div>
            
            <!-- Agent列表 -->
            <div class="card">
                <h2>Agent列表</h2>
                <div id="agentList" class="agent-list">
                    <p>加载中...</p>
                </div>

                <h2 style="margin-top: 20px;">📝 会话历史</h2>
                <div style="margin-bottom: 10px;">
                    <button type="button" class="btn" onclick="startNewConversation()" style="font-size: 12px; padding: 4px 8px;">+ 新对话</button>
                </div>
                <div id="conversationList" class="conversation-list">
                    <p>请先选择Agent</p>
                </div>
            </div>

            <!-- 流式对话 -->
            <div class="card">
                <h2>💬 流式对话</h2>
                <div class="chat-container">
                    <div id="chatMessages" class="chat-messages">
                        <div class="system-message">
                            请先选择一个Agent，然后开始对话
                        </div>
                    </div>

                    <div class="chat-input-container">
                        <div class="input-group">
                            <textarea id="chatInput" placeholder="输入消息..." rows="2"></textarea>
                            <div class="input-actions">
                                <button type="button" class="btn btn-send" onclick="sendMessage()" disabled>
                                    <span id="sendButtonText">发送</span>
                                </button>
                                <button type="button" class="btn btn-clear" onclick="clearChat()">清空</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAgentId = null;
        
        // 页面加载时获取Agent列表
        document.addEventListener('DOMContentLoaded', function() {
            loadAgents();
        });
        
        // 创建Agent表单提交
        document.getElementById('agentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            createAgent();
        });
        
        // 创建Agent
        async function createAgent() {
            const responseDiv = document.getElementById('createResponse');
            responseDiv.innerHTML = '<div class="response loading">创建中...</div>';
            
            const agentData = {
                name: document.getElementById('agentName').value,
                mode: document.getElementById('agentMode').value,
                systemPrompt: document.getElementById('systemPrompt').value,
                openingStatement: document.getElementById('openingStatement').value,
                creatorId: 1,
                modelConfig: {
                    modelName: 'qwen-turbo',
                    temperature: 0.7,
                    maxTokens: 2000,
                    topP: 0.9,
                    frequencyPenalty: 0.0,
                    presencePenalty: 0.0,
                    streamEnabled: true
                }
            };
            
            try {
                const response = await fetch('/v1/agents', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(agentData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    responseDiv.innerHTML = `<div class="response">Agent创建成功！ID: ${result.id}</div>`;
                    loadAgents(); // 刷新列表
                    document.getElementById('agentForm').reset();
                } else {
                    const error = await response.text();
                    responseDiv.innerHTML = `<div class="response error">创建失败: ${error}</div>`;
                }
            } catch (error) {
                responseDiv.innerHTML = `<div class="response error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 加载Agent列表
        async function loadAgents() {
            const listDiv = document.getElementById('agentList');
            listDiv.innerHTML = '<p>加载中...</p>';
            
            try {
                const response = await fetch('/v1/agents?userId=1');
                if (response.ok) {
                    const agents = await response.json();
                    displayAgents(agents);
                } else {
                    listDiv.innerHTML = '<p class="error">加载失败</p>';
                }
            } catch (error) {
                listDiv.innerHTML = `<p class="error">网络错误: ${error.message}</p>`;
            }
        }
        
        // 显示Agent列表
        function displayAgents(agents) {
            currentAgents = agents; // 保存agents数据
            const listDiv = document.getElementById('agentList');

            if (agents.length === 0) {
                listDiv.innerHTML = '<p>暂无Agent</p>';
                return;
            }

            const html = agents.map(agent => `
                <div class="agent-item" onclick="selectAgent(${agent.id})">
                    <div class="agent-name">${agent.name}</div>
                    <div class="agent-mode">${agent.mode === 'FUNCTION_CALLING' ? 'Function Calling' : 'ReAct'}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        ${agent.description || '无描述'}
                    </div>
                </div>
            `).join('');

            listDiv.innerHTML = html;
        }
        
        // 选择Agent
        function selectAgent(agentId) {
            selectedAgentId = agentId;
            selectedConversationId = null; // 重置会话选择

            // 更新选中状态
            document.querySelectorAll('.agent-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.agent-item').classList.add('selected');

            // 启用发送按钮并更新聊天界面
            const sendButton = document.querySelector('.btn-send');
            const chatInput = document.getElementById('chatInput');
            sendButton.disabled = false;
            chatInput.disabled = false;

            // 加载该Agent的会话历史
            loadConversations(agentId);

            // 显示Agent开场白
            const selectedAgent = getCurrentSelectedAgent();
            if (selectedAgent && selectedAgent.openingStatement) {
                clearChat();
                addMessage('assistant', selectedAgent.openingStatement);
            } else {
                clearChat();
                addMessage('system', `已选择Agent: ${selectedAgent ? selectedAgent.name : 'Unknown'}`);
            }
        }
        
        // 全局变量
        let currentAgents = [];
        let currentConversations = [];
        let selectedConversationId = null;
        let isStreaming = false;

        // 获取当前选中的Agent
        function getCurrentSelectedAgent() {
            return currentAgents.find(agent => agent.id === selectedAgentId);
        }

        // 添加消息到聊天界面
        function addMessage(role, content, streaming = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}${streaming ? ' streaming' : ''}`;
            messageDiv.textContent = content;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            return messageDiv;
        }

        // 清空聊天记录
        function clearChat() {
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.innerHTML = '';
        }

        // 发送消息
        async function sendMessage() {
            if (!selectedAgentId) {
                alert('请先选择一个Agent');
                return;
            }

            if (isStreaming) {
                return; // 正在流式输出时不允许发送新消息
            }

            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (!message) {
                return;
            }

            // 添加用户消息
            addMessage('user', message);
            chatInput.value = '';

            // 禁用输入和发送按钮
            setInputState(false);

            try {
                // 尝试流式调用
                await sendStreamMessage(message);
            } catch (error) {
                console.error('流式调用失败，尝试普通调用:', error);
                // 如果流式调用失败，尝试普通调用
                await sendNormalMessage(message);
            } finally {
                // 恢复输入状态
                setInputState(true);
            }
        }

        // 流式发送消息
        async function sendStreamMessage(message) {
            isStreaming = true;

            const requestData = {
                query: message,
                userId: 1,
                conversationId: selectedConversationId,
                stream: true
            };

            // 添加助手消息容器
            const assistantMessage = addMessage('assistant', '', true);
            let fullContent = '';

            try {
                const response = await fetch(`/v1/agents/${selectedAgentId}/execute/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    console.log('收到数据块:', chunk);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            if (data === '[DONE]') {
                                assistantMessage.classList.remove('streaming');
                                return;
                            }

                            // 处理纯Markdown格式数据
                            console.log('收到Markdown数据:', data);
                            if (data && data.trim() !== '') {
                                fullContent += data;
                                // 使用Markdown渲染
                                if (typeof marked !== 'undefined' && marked.parse) {
                                    try {
                                        const renderedContent = marked.parse(fullContent);
                                        assistantMessage.innerHTML = renderedContent;
                                        console.log('✅ Markdown渲染成功');
                                    } catch (error) {
                                        console.error('❌ Markdown渲染失败:', error);
                                        assistantMessage.textContent = fullContent;
                                    }
                                } else {
                                    console.warn('⚠️ marked库未加载，使用纯文本显示');
                                    assistantMessage.textContent = fullContent;
                                }
                                assistantMessage.parentElement.scrollTop = assistantMessage.parentElement.scrollHeight;
                            }
                        }
                    }
                }
            } catch (error) {
                assistantMessage.classList.remove('streaming');
                assistantMessage.textContent = '流式调用失败，正在尝试普通调用...';
                assistantMessage.className = 'message system';
                throw error;
            } finally {
                isStreaming = false;
            }
        }

        // 普通发送消息
        async function sendNormalMessage(message) {
            const requestData = {
                query: message,
                userId: 1,
                conversationId: selectedConversationId
            };

            // 添加加载提示
            const loadingMessage = addMessage('system', '正在思考...');

            try {
                const response = await fetch(`/v1/agents/${selectedAgentId}/execute`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                // 移除加载提示
                loadingMessage.remove();

                if (response.ok) {
                    const result = await response.json();
                    addMessage('assistant', result.content);
                } else {
                    const error = await response.text();
                    addMessage('system', `执行失败: ${error}`);
                }
            } catch (error) {
                loadingMessage.remove();
                addMessage('system', `网络错误: ${error.message}`);
            }
        }

        // 设置输入状态
        function setInputState(enabled) {
            const chatInput = document.getElementById('chatInput');
            const sendButton = document.querySelector('.btn-send');
            const sendButtonText = document.getElementById('sendButtonText');

            chatInput.disabled = !enabled;
            sendButton.disabled = !enabled || !selectedAgentId;
            sendButtonText.textContent = enabled ? '发送' : '发送中...';
        }

        // 加载会话列表
        async function loadConversations(agentId) {
            try {
                const response = await fetch(`/v1/agents/${agentId}/conversations?userId=1`);
                if (response.ok) {
                    const conversations = await response.json();
                    currentConversations = conversations;
                    displayConversations(conversations);
                } else {
                    console.error('加载会话失败:', response.statusText);
                    displayConversations([]);
                }
            } catch (error) {
                console.error('加载会话失败:', error);
                displayConversations([]);
            }
        }

        // 显示会话列表
        function displayConversations(conversations) {
            const listDiv = document.getElementById('conversationList');

            if (conversations.length === 0) {
                listDiv.innerHTML = '<p style="color: #999; font-size: 12px;">暂无历史会话</p>';
                return;
            }

            const html = conversations.map(conv => `
                <div class="conversation-item" onclick="selectConversation('${conv.uuid}')" data-conversation-id="${conv.uuid}">
                    <div class="conversation-title">${conv.title || '新对话'}</div>
                    <div class="conversation-time">${formatTime(conv.lastActiveAt)} · ${conv.messageCount}条消息</div>
                    <div class="conversation-preview" style="font-size: 11px; color: #999; margin-top: 2px;">
                        ${conv.summary || 'UUID: ' + conv.uuid.substring(0, 8) + '...'}
                    </div>
                </div>
            `).join('');

            listDiv.innerHTML = html;
        }

        // 选择会话
        async function selectConversation(conversationId) {
            selectedConversationId = conversationId;

            // 更新选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.conversation-item').classList.add('selected');

            // 显示加载提示
            clearChat();
            addMessage('system', '正在加载历史消息...');

            // 加载会话历史消息
            await loadConversationHistory(conversationId);
        }

        // 加载会话历史消息
        async function loadConversationHistory(conversationId) {
            try {
                console.log('加载会话历史:', conversationId);
                const response = await fetch(`/v1/conversations/${conversationId}/messages`);

                if (response.ok) {
                    const messages = await response.json();
                    console.log('收到历史消息:', messages);
                    displayConversationHistory(messages);
                } else {
                    console.error('加载会话历史失败:', response.status, response.statusText);
                    clearChat();
                    addMessage('system', `加载历史消息失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                console.error('加载会话历史失败:', error);
                clearChat();
                addMessage('system', `网络错误: ${error.message}`);
            }
        }

        // 显示会话历史
        function displayConversationHistory(messages) {
            clearChat();

            if (messages.length === 0) {
                addMessage('system', '这个会话还没有消息');
                return;
            }

            messages.forEach(message => {
                // 将数据库中的角色名转换为前端显示格式
                let role = message.role.toLowerCase();
                if (role === 'user') {
                    addMessage('user', message.content);
                } else if (role === 'assistant') {
                    addMessage('assistant', message.content);
                } else {
                    // 处理其他角色类型
                    addMessage('system', `[${message.role}] ${message.content}`);
                }
            });

            // 滚动到底部
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 格式化时间
        function formatTime(timeStr) {
            const date = new Date(timeStr);
            const now = new Date();
            const diff = now - date;

            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        }

        // 开始新对话
        function startNewConversation() {
            if (!selectedAgentId) {
                alert('请先选择一个Agent');
                return;
            }

            // 清除当前会话选择
            selectedConversationId = null;

            // 清除会话选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('selected');
            });

            // 显示Agent开场白
            const selectedAgent = getCurrentSelectedAgent();
            clearChat();
            if (selectedAgent && selectedAgent.openingStatement) {
                addMessage('assistant', selectedAgent.openingStatement);
            } else {
                addMessage('system', `开始与 ${selectedAgent ? selectedAgent.name : 'Agent'} 的新对话`);
            }
        }

        // 键盘事件处理
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');

            chatInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        });
    </script>
</body>
</html>
