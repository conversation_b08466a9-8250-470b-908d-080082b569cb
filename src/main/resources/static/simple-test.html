<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
            background: #f9f9f9;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
        .message.user {
            background: #007bff;
            color: white;
            text-align: right;
        }
        .message.assistant {
            background: white;
            border: 1px solid #ddd;
        }
        .streaming {
            border-left: 3px solid #007bff;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>简单流式测试</h1>
    
    <button onclick="testStream()">开始流式测试</button>
    <button onclick="clearChat()">清空</button>
    
    <div id="status" class="status">点击"开始流式测试"按钮</div>
    
    <div id="chat" class="chat-container">
        <!-- 消息将在这里显示 -->
    </div>

    <script>
        async function testStream() {
            const chatContainer = document.getElementById('chat');
            const statusDiv = document.getElementById('status');
            
            // 清空聊天容器
            chatContainer.innerHTML = '';
            statusDiv.textContent = '开始测试...';
            
            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.textContent = '你好';
            chatContainer.appendChild(userMessage);
            
            // 添加助手消息容器
            const assistantMessage = document.createElement('div');
            assistantMessage.className = 'message assistant streaming';
            assistantMessage.textContent = '';
            chatContainer.appendChild(assistantMessage);
            
            let fullContent = '';
            
            try {
                statusDiv.textContent = '发送请求...';
                
                const response = await fetch('/v1/agents/4/execute/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        query: '你好',
                        userId: 1,
                        stream: true
                    })
                });
                
                statusDiv.textContent = `HTTP状态: ${response.status} ${response.statusText}`;
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let chunkCount = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    chunkCount++;
                    const chunk = decoder.decode(value, { stream: true });
                    statusDiv.textContent = `收到数据块 ${chunkCount}: ${chunk.length} 字符`;
                    
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            const data = line.slice(5);
                            
                            if (data === '[DONE]') {
                                assistantMessage.classList.remove('streaming');
                                statusDiv.textContent = '✅ 流式传输完成';
                                return;
                            }
                            
                            // 处理纯Markdown格式数据
                            if (data && data.trim() !== '') {
                                fullContent += data;
                                assistantMessage.textContent = fullContent;
                                chatContainer.scrollTop = chatContainer.scrollHeight;
                                statusDiv.textContent = '✅ 正在接收数据...';
                            }
                        }
                    }
                }
                
            } catch (error) {
                assistantMessage.classList.remove('streaming');
                assistantMessage.textContent = `❌ 错误: ${error.message}`;
                assistantMessage.style.background = '#ffebee';
                statusDiv.textContent = `❌ 错误: ${error.message}`;
            }
        }
        
        function clearChat() {
            document.getElementById('chat').innerHTML = '';
            document.getElementById('status').textContent = '已清空';
        }
    </script>
</body>
</html>
