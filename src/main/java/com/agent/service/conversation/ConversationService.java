package com.agent.service.conversation;

import com.agent.domain.entity.Conversation;
import com.agent.domain.entity.Message;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 会话服务接口
 */
public interface ConversationService {
    
    /**
     * 创建或获取会话
     */
    Mono<Conversation> getOrCreateConversation(Long agentId, Long userId, String conversationId);
    
    /**
     * 保存消息
     */
    Mono<Message> saveMessage(String conversationId, String role, String content);
    
    /**
     * 获取会话历史消息
     */
    Mono<List<Message>> getConversationHistory(String conversationId, int limit);
    
    /**
     * 获取用户的会话列表
     */
    Mono<List<Conversation>> getUserConversations(Long userId, Long agentId);
}
