package com.agent.service.llm;

import com.agent.domain.dto.ChatRequest;
import com.agent.domain.dto.ChatResponse;
import com.agent.domain.dto.EmbeddingRequest;
import com.agent.domain.dto.EmbeddingResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 模拟LLM服务 - 用于快速测试
 */
@Service
@ConditionalOnProperty(name = "llm.mock.enabled", havingValue = "true", matchIfMissing = false)
@Slf4j
public class MockLLMService implements LLMService {
    
    @Override
    public Mono<ChatResponse> chat(ChatRequest request) {
        log.debug("模拟聊天请求: {}", request.getMessages().size());
        
        return Mono.delay(Duration.ofMillis(500))
                .map(delay -> {
                    ChatResponse response = new ChatResponse();
                    response.setId("mock-" + System.currentTimeMillis());
                    response.setModel(request.getModel());
                    response.setCreated(System.currentTimeMillis() / 1000);
                    response.setRole("assistant");
                    
                    // 根据最后一条用户消息生成回复
                    String userMessage = getLastUserMessage(request.getMessages());
                    response.setContent(generateMockResponse(userMessage));
                    response.setFinishReason("stop");
                    
                    // 模拟token使用
                    ChatResponse.Usage usage = new ChatResponse.Usage();
                    usage.setPromptTokens(100);
                    usage.setCompletionTokens(50);
                    usage.setTotalTokens(150);
                    response.setUsage(usage);
                    
                    return response;
                });
    }
    
    @Override
    public Flux<ChatResponse> chatStream(ChatRequest request) {
        log.debug("模拟流式聊天请求: {}", request.getMessages().size());

        String userMessage = getLastUserMessage(request.getMessages());
        String fullResponse = generateMockResponse(userMessage);

        // 将响应分成多个块进行流式输出，按字符分割以获得更好的流式效果
        char[] chars = fullResponse.toCharArray();

        return Flux.range(0, chars.length)
                .delayElements(Duration.ofMillis(50)) // 更快的流式速度
                .map(i -> {
                    ChatResponse response = new ChatResponse();
                    response.setId("mock-stream-" + System.currentTimeMillis());
                    response.setModel(request.getModel());
                    response.setCreated(System.currentTimeMillis() / 1000);
                    response.setRole("assistant");
                    response.setContent(String.valueOf(chars[i]));
                    response.setIsStream(true);

                    if (i == chars.length - 1) {
                        response.setFinishReason("stop");
                    }

                    return response;
                });
    }
    
    @Override
    public Mono<EmbeddingResponse> embedding(EmbeddingRequest request) {
        log.debug("模拟嵌入向量请求");
        
        return Mono.delay(Duration.ofMillis(200))
                .map(delay -> {
                    EmbeddingResponse response = new EmbeddingResponse();
                    response.setModel(request.getModel());
                    response.setIndex(0);
                    
                    // 生成模拟向量
                    List<Double> vector = new ArrayList<>();
                    for (int i = 0; i < 1536; i++) {
                        vector.add(Math.random() * 2 - 1); // -1 到 1 之间的随机数
                    }
                    response.setEmbedding(vector);
                    
                    // 模拟使用统计
                    EmbeddingResponse.Usage usage = new EmbeddingResponse.Usage();
                    usage.setPromptTokens(10);
                    usage.setTotalTokens(10);
                    response.setUsage(usage);
                    
                    return response;
                });
    }
    
    @Override
    public Mono<EmbeddingResponse> batchEmbedding(EmbeddingRequest request) {
        return embedding(request);
    }
    
    @Override
    public Mono<Boolean> isModelAvailable(String modelName) {
        return Mono.just(true);
    }
    
    @Override
    public Mono<List<String>> getSupportedModels() {
        return Mono.just(List.of("mock-model", "qwen-turbo", "qwen-plus"));
    }
    
    private String getLastUserMessage(List<ChatRequest.Message> messages) {
        for (int i = messages.size() - 1; i >= 0; i--) {
            ChatRequest.Message message = messages.get(i);
            if ("user".equals(message.getRole())) {
                return message.getContent();
            }
        }
        return "你好";
    }
    
    private String generateMockResponse(String userMessage) {
        if (userMessage == null || userMessage.trim().isEmpty()) {
            return "你好！我是AI助手，有什么可以帮助你的吗？";
        }
        
        userMessage = userMessage.toLowerCase();
        
        if (userMessage.contains("你好") || userMessage.contains("hello")) {
            return "你好！我是AI助手，很高兴为你服务。有什么问题我可以帮你解答吗？";
        } else if (userMessage.contains("天气")) {
            return "我是一个模拟的AI助手，无法获取实时天气信息。建议你查看天气预报应用获取准确的天气信息。";
        } else if (userMessage.contains("计算") || userMessage.contains("数学")) {
            return "我可以帮你进行基本的数学计算。请告诉我具体需要计算什么？";
        } else if (userMessage.contains("搜索")) {
            return "我可以帮你搜索信息。请告诉我你想搜索什么内容？";
        } else {
            return "这是一个模拟回复。你说：\"" + userMessage + "\"。我理解了你的问题，这里是我的回答。作为AI助手，我会尽力帮助你解决问题。";
        }
    }
}
