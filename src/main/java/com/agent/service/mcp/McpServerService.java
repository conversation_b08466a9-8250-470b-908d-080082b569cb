package com.agent.service.mcp;

import com.agent.domain.entity.AgentMcpServer;
import com.agent.domain.entity.McpServer;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * MCP Server服务接口
 */
public interface McpServerService {

    /**
     * 创建MCP Server
     */
    Mono<McpServer> createMcpServer(McpServer mcpServer);

    /**
     * 更新MCP Server
     */
    Mono<McpServer> updateMcpServer(Long id, McpServer mcpServer);

    /**
     * 删除MCP Server
     */
    Mono<Void> deleteMcpServer(Long id);

    /**
     * 根据ID获取MCP Server
     */
    Mono<McpServer> getMcpServerById(Long id);

    /**
     * 获取用户的MCP Server列表
     */
    Flux<McpServer> getMcpServersByCreator(Long creatorId);

    /**
     * 测试MCP Server连接
     */
    Mono<Boolean> testMcpServerConnection(McpServer mcpServer);

    /**
     * 为Agent配置MCP Server
     */
    Mono<Void> configureAgentMcpServers(Long agentId, List<AgentMcpServer> mcpServers);

    /**
     * 获取Agent的MCP Server配置
     */
    Flux<AgentMcpServer> getAgentMcpServers(Long agentId);

    /**
     * 调用MCP Server工具
     */
    Mono<String> callMcpTool(Long mcpServerId, String toolName, Map<String, Object> arguments);

    /**
     * 获取MCP Server的可用工具列表
     */
    Mono<List<Map<String, Object>>> getMcpServerTools(Long mcpServerId);
}
