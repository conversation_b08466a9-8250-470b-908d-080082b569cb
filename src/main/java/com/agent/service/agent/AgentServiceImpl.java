package com.agent.service.agent;

import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.domain.entity.Agent;
import com.agent.repository.AgentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Agent服务实现
 */
@Service
@Slf4j
public class AgentServiceImpl implements AgentService {

    private final AgentRepository agentRepository;
    private final List<AgentExecutor> agentExecutors;
    private final Map<Agent.AgentMode, AgentExecutor> executorMap;

    // 构造函数中初始化执行器映射
    public AgentServiceImpl(AgentRepository agentRepository, List<AgentExecutor> agentExecutors) {
        this.agentRepository = agentRepository;
        this.agentExecutors = agentExecutors;
        this.executorMap = agentExecutors.stream()
                .collect(Collectors.toMap(AgentExecutor::getSupportedMode, Function.identity()));
    }
    
    @Override
    public Mono<Agent> createAgent(Agent agent) {
        log.debug("创建Agent: {}", agent.getName());
        
        agent.setCreatedAt(LocalDateTime.now());
        agent.setUpdatedAt(LocalDateTime.now());
        agent.setStatus(Agent.AgentStatus.DRAFT);
        
        return Mono.fromCallable(() -> agentRepository.save(agent))
                .doOnSuccess(savedAgent -> log.info("Agent创建成功: {}", savedAgent.getId()))
                .doOnError(error -> log.error("Agent创建失败", error));
    }
    
    @Override
    public Mono<Agent> updateAgent(Agent agent) {
        log.debug("更新Agent: {}", agent.getId());
        
        return getAgentById(agent.getId())
                .flatMap(existingAgent -> {
                    // 更新字段
                    existingAgent.setName(agent.getName());
                    existingAgent.setDescription(agent.getDescription());
                    existingAgent.setSystemPrompt(agent.getSystemPrompt());
                    existingAgent.setOpeningStatement(agent.getOpeningStatement());
                    existingAgent.setModelConfig(agent.getModelConfig());
                    existingAgent.setUpdatedAt(LocalDateTime.now());
                    
                    return Mono.fromCallable(() -> agentRepository.save(existingAgent));
                })
                .doOnSuccess(updatedAgent -> log.info("Agent更新成功: {}", updatedAgent.getId()))
                .doOnError(error -> log.error("Agent更新失败", error));
    }
    
    @Override
    public Mono<Void> deleteAgent(Long agentId) {
        log.debug("删除Agent: {}", agentId);
        
        return getAgentById(agentId)
                .flatMap(agent -> {
                    return Mono.fromRunnable(() -> agentRepository.delete(agent)).then();
                })
                .doOnSuccess(v -> log.info("Agent删除成功: {}", agentId))
                .doOnError(error -> log.error("Agent删除失败", error));
    }
    
    @Override
    public Mono<Agent> getAgentById(Long agentId) {
        return Mono.fromCallable(() -> agentRepository.findById(agentId))
                .map(optional -> optional.orElseThrow(() -> 
                        new IllegalArgumentException("Agent不存在: " + agentId)));
    }
    
    @Override
    public Mono<List<Agent>> getAgentsByUserId(Long userId) {
        return Mono.fromCallable(() -> agentRepository.findByCreatorIdOrderByCreatedAtDesc(userId));
    }
    
    @Override
    public Mono<Agent> publishAgent(Long agentId) {
        log.debug("发布Agent: {}", agentId);
        
        return getAgentById(agentId)
                .flatMap(agent -> {
                    if (agent.getStatus() == Agent.AgentStatus.PUBLISHED) {
                        return Mono.just(agent);
                    }
                    
                    // 验证Agent配置
                    return validateAgent(agentId)
                            .flatMap(isValid -> {
                                if (!isValid) {
                                    return Mono.error(new IllegalStateException("Agent配置无效，无法发布"));
                                }
                                
                                agent.setStatus(Agent.AgentStatus.PUBLISHED);
                                agent.setUpdatedAt(LocalDateTime.now());
                                
                                return Mono.fromCallable(() -> agentRepository.save(agent));
                            });
                })
                .doOnSuccess(publishedAgent -> log.info("Agent发布成功: {}", publishedAgent.getId()))
                .doOnError(error -> log.error("Agent发布失败", error));
    }
    
    @Override
    public Mono<Agent> archiveAgent(Long agentId) {
        log.debug("归档Agent: {}", agentId);
        
        return getAgentById(agentId)
                .flatMap(agent -> {
                    agent.setStatus(Agent.AgentStatus.ARCHIVED);
                    agent.setUpdatedAt(LocalDateTime.now());
                    
                    return Mono.fromCallable(() -> agentRepository.save(agent));
                })
                .doOnSuccess(archivedAgent -> log.info("Agent归档成功: {}", archivedAgent.getId()))
                .doOnError(error -> log.error("Agent归档失败", error));
    }
    
    @Override
    public Mono<AgentExecutionResponse> executeAgent(AgentExecutionRequest request) {
        Agent agent = request.getAgent();
        log.debug("执行Agent: agentId={}, mode={}", agent.getId(), agent.getMode());

        AgentExecutor executor = executorMap.get(agent.getMode());

        if (executor == null) {
            return Mono.error(new IllegalArgumentException("不支持的Agent模式: " + agent.getMode()));
        }

        long startTime = System.currentTimeMillis();

        return executor.execute(request)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    response.setExecutionTime(executionTime);
                    return response;
                })
                .timeout(java.time.Duration.ofSeconds(request.getMaxExecutionTime() != null ? request.getMaxExecutionTime() : 300))
                .doOnSuccess(response -> log.info("Agent执行成功: {} ({}ms)",
                        agent.getId(), response.getExecutionTime()))
                .doOnError(error -> log.error("Agent执行失败: {}", agent.getId(), error));
    }
    
    @Override
    public Flux<AgentExecutionResponse> executeAgentStream(AgentExecutionRequest request) {
        Agent agent = request.getAgent();
        log.debug("流式执行Agent: agentId={}, mode={}", agent.getId(), agent.getMode());

        AgentExecutor executor = executorMap.get(agent.getMode());

        if (executor == null) {
            return Flux.error(new IllegalArgumentException("不支持的Agent模式: " + agent.getMode()));
        }

        long startTime = System.currentTimeMillis();

        return executor.executeStream(request)
                .map(response -> {
                    if (response.getFinished()) {
                        long executionTime = System.currentTimeMillis() - startTime;
                        response.setExecutionTime(executionTime);
                    }
                    return response;
                })
                .timeout(java.time.Duration.ofSeconds(request.getMaxExecutionTime() != null ? request.getMaxExecutionTime() : 300))
                .doOnComplete(() -> log.info("Agent流式执行完成: {}", agent.getId()))
                .doOnError(error -> log.error("Agent流式执行失败: {}", agent.getId(), error));
    }
    
    @Override
    public Mono<Boolean> validateAgent(Long agentId) {
        return getAgentById(agentId)
                .flatMap(agent -> {
                    AgentExecutor executor = executorMap.get(agent.getMode());
                    if (executor == null) {
                        return Mono.just(false);
                    }
                    return executor.validateAgentConfig(agent);
                });
    }
    
    @Override
    public Mono<Agent> cloneAgent(Long agentId, Long newOwnerId) {
        log.debug("复制Agent: {} -> {}", agentId, newOwnerId);
        
        return getAgentById(agentId)
                .map(originalAgent -> {
                    Agent clonedAgent = new Agent();
                    clonedAgent.setName(originalAgent.getName() + " (副本)");
                    clonedAgent.setDescription(originalAgent.getDescription());
                    clonedAgent.setMode(originalAgent.getMode());
                    clonedAgent.setSystemPrompt(originalAgent.getSystemPrompt());
                    clonedAgent.setOpeningStatement(originalAgent.getOpeningStatement());
                    clonedAgent.setModelConfig(originalAgent.getModelConfig());
                    clonedAgent.setCreatorId(newOwnerId);
                    clonedAgent.setStatus(Agent.AgentStatus.DRAFT);
                    
                    return clonedAgent;
                })
                .flatMap(this::createAgent)
                .doOnSuccess(clonedAgent -> log.info("Agent复制成功: {} -> {}", 
                        agentId, clonedAgent.getId()))
                .doOnError(error -> log.error("Agent复制失败", error));
    }
}
