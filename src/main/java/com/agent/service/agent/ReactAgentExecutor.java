package com.agent.service.agent;

import com.agent.domain.entity.Agent;
import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.domain.dto.ChatRequest;
import com.agent.service.llm.LLMService;
import com.agent.service.tool.ToolService;
import com.agent.service.knowledge.KnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * ReAct模式Agent执行器
 * 
 * ReAct (Reasoning and Acting) 模式：
 * 1. Thought: 思考当前情况和下一步行动
 * 2. Action: 执行具体的行动（调用工具或搜索知识库）
 * 3. Observation: 观察行动的结果
 * 4. 重复上述过程直到得到最终答案
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReactAgentExecutor implements AgentExecutor {
    
    private final LLMService llmService;
    private final ToolService toolService;
    private final KnowledgeService knowledgeService;
    
    private static final String REACT_PROMPT_TEMPLATE = """
        你是一个智能助手，需要使用ReAct (Reasoning and Acting) 方法来回答用户问题。
        
        你可以使用以下工具：
        {tools}
        
        你可以访问以下知识库：
        {knowledge_bases}
        
        请按照以下格式进行推理：
        
        Thought: 我需要思考如何回答这个问题
        Action: 选择一个行动（使用工具或搜索知识库）
        Observation: 观察行动的结果
        ... (重复Thought/Action/Observation)
        Final Answer: 最终答案
        
        重要规则：
        1. 每次只能执行一个Action
        2. 必须等待Observation后再进行下一步
        3. 如果无法通过工具或知识库获得答案，请基于你的知识回答
        4. 最多进行10轮推理
        
        用户问题：{query}
        """;
    
    @Override
    public Agent.AgentMode getSupportedMode() {
        return Agent.AgentMode.REACT;
    }
    
    @Override
    public Mono<AgentExecutionResponse> execute(AgentExecutionRequest request) {
        log.debug("开始执行ReAct模式Agent: agentId={}, query={}",
                request.getAgent() != null ? request.getAgent().getId() : null,
                request.getQuery());

        return validateAgentConfig(request.getAgent())
                .flatMap(valid -> {
                    if (!valid) {
                        return Mono.error(new IllegalArgumentException("Agent配置无效"));
                    }
                    return executeReActLoop(request, 0);
                })
                .doOnSuccess(response -> log.debug("ReAct执行完成: finished={}", response.getFinished()))
                .doOnError(error -> log.error("ReAct执行失败", error));
    }
    
    @Override
    public Flux<AgentExecutionResponse> executeStream(AgentExecutionRequest request) {
        log.debug("开始流式执行ReAct模式Agent: agentId={}, query={}",
                request.getAgent() != null ? request.getAgent().getId() : null,
                request.getQuery());

        return validateAgentConfig(request.getAgent())
                .flatMapMany(valid -> {
                    if (!valid) {
                        return Flux.error(new IllegalArgumentException("Agent配置无效"));
                    }
                    return executeReActLoopStream(request, 0);
                })
                .doOnComplete(() -> log.debug("ReAct流式执行完成"))
                .doOnError(error -> log.error("ReAct流式执行失败", error));
    }
    
    @Override
    public Mono<Boolean> validateAgentConfig(Agent agent) {
        if (agent == null) {
            return Mono.just(false);
        }
        
        if (agent.getMode() != Agent.AgentMode.REACT) {
            return Mono.just(false);
        }
        
        if (agent.getSystemPrompt() == null || agent.getSystemPrompt().trim().isEmpty()) {
            return Mono.just(false);
        }
        
        return Mono.just(true);
    }
    
    private Mono<AgentExecutionResponse> executeReActLoop(AgentExecutionRequest request, int iteration) {
        if (iteration >= 10) {
            return Mono.just(AgentExecutionResponse.builder()
                    .content("达到最大推理轮数，无法继续执行")
                    .finished(true)
                    .error("MAX_ITERATIONS_REACHED")
                    .build());
        }
        
        // 构建ReAct提示词
        String prompt = buildReActPrompt(request);
        
        // 调用LLM进行推理
        return callLLMForReasoning(request, prompt)
                .flatMap(response -> {
                    // 解析响应，判断是否需要执行Action
                    if (isActionRequired(response.getContent())) {
                        return executeAction(request, response.getContent())
                                .flatMap(actionResult -> {
                                    // 将Action结果添加到对话历史中
                                    String updatedPrompt = prompt + "\n" + response.getContent() + 
                                                         "\nObservation: " + actionResult;
                                    
                                    // 继续下一轮推理
                                    AgentExecutionRequest updatedRequest = request.toBuilder()
                                            .query(updatedPrompt)
                                            .build();
                                    
                                    return executeReActLoop(updatedRequest, iteration + 1);
                                });
                    } else {
                        // 已经得到最终答案
                        return Mono.just(AgentExecutionResponse.builder()
                                .content(extractFinalAnswer(response.getContent()))
                                .finished(true)
                                .reasoning(response.getContent())
                                .build());
                    }
                });
    }
    
    private Flux<AgentExecutionResponse> executeReActLoopStream(AgentExecutionRequest request, int iteration) {
        if (iteration >= 10) {
            return Flux.just(AgentExecutionResponse.builder()
                    .content("达到最大推理轮数，无法继续执行")
                    .finished(true)
                    .error("MAX_ITERATIONS_REACHED")
                    .build());
        }
        
        String prompt = buildReActPrompt(request);
        
        return callLLMForReasoningStream(request, prompt)
                .flatMap(response -> {
                    if (isActionRequired(response.getContent())) {
                        return executeAction(request, response.getContent())
                                .flatMapMany(actionResult -> {
                                    // 发送Action执行结果
                                    AgentExecutionResponse actionResponse = AgentExecutionResponse.builder()
                                            .content("Observation: " + actionResult)
                                            .finished(false)
                                            .stepType("observation")
                                            .build();
                                    
                                    String updatedPrompt = prompt + "\n" + response.getContent() + 
                                                         "\nObservation: " + actionResult;
                                    
                                    AgentExecutionRequest updatedRequest = request.toBuilder()
                                            .query(updatedPrompt)
                                            .build();
                                    
                                    return Flux.concat(
                                            Flux.just(actionResponse),
                                            executeReActLoopStream(updatedRequest, iteration + 1)
                                    );
                                });
                    } else {
                        return Flux.just(AgentExecutionResponse.builder()
                                .content(extractFinalAnswer(response.getContent()))
                                .finished(true)
                                .reasoning(response.getContent())
                                .build());
                    }
                });
    }
    
    private String buildReActPrompt(AgentExecutionRequest request) {
        Agent agent = request.getAgent();
        
        // 获取可用工具列表
        String toolsDescription = toolService.getAvailableToolsDescription(agent.getId());
        
        // 获取知识库描述
        String knowledgeBasesDescription = knowledgeService.getKnowledgeBasesDescription(agent.getId());
        
        return REACT_PROMPT_TEMPLATE
                .replace("{tools}", toolsDescription)
                .replace("{knowledge_bases}", knowledgeBasesDescription)
                .replace("{query}", request.getQuery());
    }
    
    private Mono<AgentExecutionResponse> callLLMForReasoning(AgentExecutionRequest request, String prompt) {
        Agent agent = request.getAgent();

        // 构建ChatRequest
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setModel(agent.getModelConfig().getModelName());
        chatRequest.setTemperature(agent.getModelConfig().getTemperature());
        chatRequest.setMaxTokens(agent.getModelConfig().getMaxTokens());
        chatRequest.setTopP(agent.getModelConfig().getTopP());

        // 构建消息列表，包含历史对话
        List<ChatRequest.Message> messages = buildMessagesWithHistory(request, prompt);
        chatRequest.setMessages(messages);

        return llmService.chat(chatRequest)
                .map(response -> AgentExecutionResponse.builder()
                        .content(response.getContent())
                        .finished(response.getFinishReason() != null)
                        .stepType("thought")
                        .tokenUsage(response.getUsage() != null ?
                                   AgentExecutionResponse.TokenUsage.builder()
                                           .promptTokens(response.getUsage().getPromptTokens())
                                           .completionTokens(response.getUsage().getCompletionTokens())
                                           .totalTokens(response.getUsage().getTotalTokens())
                                           .build() : null)
                        .build());
    }
    
    private Flux<AgentExecutionResponse> callLLMForReasoningStream(AgentExecutionRequest request, String prompt) {
        Agent agent = request.getAgent();

        // 构建ChatRequest
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setModel(agent.getModelConfig().getModelName());
        chatRequest.setTemperature(agent.getModelConfig().getTemperature());
        chatRequest.setMaxTokens(agent.getModelConfig().getMaxTokens());
        chatRequest.setTopP(agent.getModelConfig().getTopP());
        chatRequest.setStream(true);

        // 构建消息列表，包含历史对话
        List<ChatRequest.Message> messages = buildMessagesWithHistory(request, prompt);
        chatRequest.setMessages(messages);

        return llmService.chatStream(chatRequest)
                .map(response -> AgentExecutionResponse.builder()
                        .content(response.getContent())
                        .finished(response.getFinishReason() != null)
                        .stepType("thought")
                        .isStream(true)
                        .build());
    }

    private List<ChatRequest.Message> buildMessagesWithHistory(AgentExecutionRequest request, String prompt) {
        List<ChatRequest.Message> messages = new ArrayList<>();

        // 添加系统消息
        if (request.getAgent().getSystemPrompt() != null) {
            ChatRequest.Message systemMessage = new ChatRequest.Message();
            systemMessage.setRole("system");
            systemMessage.setContent(request.getAgent().getSystemPrompt());
            messages.add(systemMessage);
        }

        // TODO: 这里应该从数据库加载历史对话记录
        // 暂时只添加当前用户消息
        ChatRequest.Message userMessage = new ChatRequest.Message();
        userMessage.setRole("user");
        userMessage.setContent(prompt);
        messages.add(userMessage);

        return messages;
    }
    
    private boolean isActionRequired(String content) {
        return content.contains("Action:") && !content.contains("Final Answer:");
    }
    
    private Mono<String> executeAction(AgentExecutionRequest request, String reasoning) {
        // 解析Action并执行
        String action = extractAction(reasoning);
        
        if (action.startsWith("search_knowledge")) {
            // 搜索知识库
            return knowledgeService.search(request.getAgent().getId(), extractSearchQuery(action));
        } else if (action.startsWith("use_tool")) {
            // 使用工具
            return toolService.executeTool(extractToolName(action), extractToolParams(action));
        } else {
            return Mono.just("无法识别的Action: " + action);
        }
    }
    
    private String extractAction(String reasoning) {
        // 从推理文本中提取Action
        String[] lines = reasoning.split("\n");
        for (String line : lines) {
            if (line.trim().startsWith("Action:")) {
                return line.substring(line.indexOf("Action:") + 7).trim();
            }
        }
        return "";
    }
    
    private String extractFinalAnswer(String content) {
        // 从内容中提取最终答案
        String[] lines = content.split("\n");
        for (String line : lines) {
            if (line.trim().startsWith("Final Answer:")) {
                return line.substring(line.indexOf("Final Answer:") + 13).trim();
            }
        }
        return content;
    }
    
    private String extractSearchQuery(String action) {
        // 从action中提取搜索查询
        return action.substring(action.indexOf("(") + 1, action.lastIndexOf(")"));
    }
    
    private String extractToolName(String action) {
        // 从action中提取工具名称
        return action.substring(action.indexOf("(") + 1, action.indexOf(",")).trim();
    }
    
    private String extractToolParams(String action) {
        // 从action中提取工具参数
        return action.substring(action.indexOf(",") + 1, action.lastIndexOf(")")).trim();
    }
}
