package com.agent.service.tool;

import com.agent.domain.dto.ChatRequest;
import com.agent.service.mcp.McpServerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工具服务实现 - 简化版本，支持MCP服务配置
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ToolServiceImpl implements ToolService {

    private final ObjectMapper objectMapper;
    private final McpServerService mcpServerService;

    // 模拟的内置工具
    private final Map<String, ToolDefinition> builtinTools = new HashMap<>();

    // 使用@PostConstruct初始化内置工具
    @jakarta.annotation.PostConstruct
    private void initializeBuiltinTools() {
        // 搜索工具
        ToolDefinition searchTool = new ToolDefinition();
        searchTool.name = "web_search";
        searchTool.description = "搜索互联网信息";
        searchTool.parameters = createSearchParameters();
        builtinTools.put("web_search", searchTool);
        
        // 计算器工具
        ToolDefinition calculatorTool = new ToolDefinition();
        calculatorTool.name = "calculator";
        calculatorTool.description = "执行数学计算";
        calculatorTool.parameters = createCalculatorParameters();
        builtinTools.put("calculator", calculatorTool);
        
        // MCP服务工具示例
        ToolDefinition mcpTool = new ToolDefinition();
        mcpTool.name = "mcp_service";
        mcpTool.description = "调用MCP服务";
        mcpTool.parameters = createMcpParameters();
        builtinTools.put("mcp_service", mcpTool);
    }
    
    @Override
    public Mono<List<ChatRequest.Tool>> getAvailableTools(Long agentId) {
        log.info("🔧 开始获取Agent可用工具: agentId={}", agentId);

        List<ChatRequest.Tool> tools = new ArrayList<>();

        // 添加内置工具（除了mcp_service）
        for (ToolDefinition toolDef : builtinTools.values()) {
            if (!"mcp_service".equals(toolDef.name)) {  // 跳过通用MCP工具
                ChatRequest.Tool tool = new ChatRequest.Tool();
                tool.setType("function");

                ChatRequest.Tool.Function function = new ChatRequest.Tool.Function();
                function.setName(toolDef.name);
                function.setDescription(toolDef.description);
                function.setParameters(toolDef.parameters);

                tool.setFunction(function);
                tools.add(tool);
                log.debug("✅ 添加内置工具: {}", toolDef.name);
            }
        }

        log.info("📋 内置工具数量: {}", tools.size());

        // 添加MCP Server的工具
        return mcpServerService.getAgentMcpServers(agentId)
                .doOnNext(agentMcpServer -> log.info("🔍 找到Agent MCP Server配置: serverId={}, enabled={}",
                        agentMcpServer.getMcpServerId(), agentMcpServer.getEnabled()))
                .filter(agentMcpServer -> agentMcpServer.getEnabled())
                .doOnNext(agentMcpServer -> log.info("✅ 启用的MCP Server: serverId={}", agentMcpServer.getMcpServerId()))
                .flatMap(agentMcpServer -> {
                    log.info("🚀 开始获取MCP Server工具: serverId={}", agentMcpServer.getMcpServerId());
                    return mcpServerService.getMcpServerTools(agentMcpServer.getMcpServerId())
                            .doOnNext(mcpTools -> log.info("📦 获取到MCP工具: serverId={}, 工具数量={}",
                                    agentMcpServer.getMcpServerId(), mcpTools.size()))
                            .onErrorResume(error -> {
                                log.error("❌ 获取MCP Server工具失败: serverId={}, error={}",
                                        agentMcpServer.getMcpServerId(), error.getMessage(), error);
                                return Mono.just(new ArrayList<>());
                            });
                })
                .collectList()
                .map(mcpToolsList -> {
                    log.info("🔄 开始转换MCP工具: 总列表数={}", mcpToolsList.size());

                    // 将MCP工具转换为ChatRequest.Tool格式
                    int mcpToolCount = 0;
                    for (List<Map<String, Object>> mcpTools : mcpToolsList) {
                        log.info("📋 处理MCP工具列表: 工具数量={}", mcpTools.size());
                        for (Map<String, Object> mcpTool : mcpTools) {
                            ChatRequest.Tool tool = convertMcpToolToChatTool(mcpTool);
                            if (tool != null) {
                                tools.add(tool);
                                mcpToolCount++;
                                log.info("✅ 成功转换MCP工具: {}", tool.getFunction().getName());
                            } else {
                                log.warn("❌ MCP工具转换失败: {}", mcpTool);
                            }
                        }
                    }

                    log.info("🎯 Agent {}工具统计: 内置工具={}, MCP工具={}, 总计={}",
                            agentId, tools.size() - mcpToolCount, mcpToolCount, tools.size());
                    return tools;
                });
    }

    /**
     * 将MCP工具转换为ChatRequest.Tool格式
     */
    @SuppressWarnings("unchecked")
    private ChatRequest.Tool convertMcpToolToChatTool(Map<String, Object> mcpTool) {
        try {
            // MCP工具格式: {type: "function", function: {name: "add", description: "...", parameters: {...}}}
            Map<String, Object> functionInfo = (Map<String, Object>) mcpTool.get("function");
            if (functionInfo == null) {
                log.warn("MCP工具缺少function字段: {}", mcpTool);
                return null;
            }

            String name = (String) functionInfo.get("name");
            String description = (String) functionInfo.get("description");
            Map<String, Object> parameters = (Map<String, Object>) functionInfo.get("parameters");

            if (name == null || name.isEmpty()) {
                log.warn("MCP工具缺少名称: {}", mcpTool);
                return null;
            }

            ChatRequest.Tool tool = new ChatRequest.Tool();
            tool.setType("function");

            ChatRequest.Tool.Function function = new ChatRequest.Tool.Function();
            function.setName(name);
            function.setDescription(description != null ? description : "MCP工具: " + name);
            function.setParameters(parameters != null ? parameters : createDefaultParameters());

            tool.setFunction(function);

            log.info("✅ 转换MCP工具成功: name={}, description={}", name, description);
            return tool;
        } catch (Exception e) {
            log.error("❌ 转换MCP工具失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建默认参数结构
     */
    private Map<String, Object> createDefaultParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        parameters.put("properties", new HashMap<>());
        parameters.put("required", new ArrayList<>());
        return parameters;
    }

    @Override
    public String getAvailableToolsDescription(Long agentId) {
        StringBuilder description = new StringBuilder();
        description.append("可用工具列表：\n");
        
        for (ToolDefinition toolDef : builtinTools.values()) {
            description.append("- ").append(toolDef.name)
                      .append(": ").append(toolDef.description).append("\n");
        }
        
        return description.toString();
    }
    
    @Override
    public Mono<String> executeTool(String toolName, String parameters) {
        log.debug("执行工具: {} with parameters: {}", toolName, parameters);
        
        return switch (toolName) {
            case "web_search" -> executeWebSearch(parameters);
            case "calculator" -> executeCalculator(parameters);
            case "mcp_service" -> executeMcpService(parameters);
            default -> Mono.just("未知工具: " + toolName);
        };
    }
    
    @Override
    public Mono<Boolean> validateToolParameters(String toolName, String parameters) {
        try {
            ToolDefinition toolDef = builtinTools.get(toolName);
            if (toolDef == null) {
                return Mono.just(false);
            }
            
            // 简单的参数验证
            Map<String, Object> params = objectMapper.readValue(parameters, Map.class);
            return Mono.just(params != null);
        } catch (Exception e) {
            log.error("参数验证失败", e);
            return Mono.just(false);
        }
    }
    
    @Override
    public Mono<String> getToolParameterSchema(String toolName) {
        ToolDefinition toolDef = builtinTools.get(toolName);
        if (toolDef == null) {
            return Mono.just("{}");
        }
        
        try {
            return Mono.just(objectMapper.writeValueAsString(toolDef.parameters));
        } catch (Exception e) {
            log.error("获取工具参数Schema失败", e);
            return Mono.just("{}");
        }
    }
    
    private Mono<String> executeWebSearch(String parameters) {
        try {
            Map<String, Object> params = objectMapper.readValue(parameters, Map.class);
            String query = (String) params.get("query");
            
            // 模拟搜索结果
            return Mono.just("搜索结果: 关于 '" + query + "' 的相关信息...");
        } catch (Exception e) {
            return Mono.just("搜索执行失败: " + e.getMessage());
        }
    }
    
    private Mono<String> executeCalculator(String parameters) {
        try {
            Map<String, Object> params = objectMapper.readValue(parameters, Map.class);
            String expression = (String) params.get("expression");
            
            // 简单的数学计算 (实际应该使用安全的表达式解析器)
            double result = evaluateSimpleExpression(expression);
            return Mono.just("计算结果: " + result);
        } catch (Exception e) {
            return Mono.just("计算执行失败: " + e.getMessage());
        }
    }
    
    private Mono<String> executeMcpService(String parameters) {
        try {
            Map<String, Object> params = objectMapper.readValue(parameters, Map.class);
            String service = (String) params.get("service");
            String method = (String) params.get("method");
            
            // 模拟MCP服务调用
            return Mono.just("MCP服务调用结果: " + service + "." + method + " 执行成功");
        } catch (Exception e) {
            return Mono.just("MCP服务调用失败: " + e.getMessage());
        }
    }
    
    private double evaluateSimpleExpression(String expression) {
        // 简化的表达式计算，实际应该使用更安全的解析器
        expression = expression.replaceAll("\\s", "");
        
        if (expression.contains("+")) {
            String[] parts = expression.split("\\+");
            return Double.parseDouble(parts[0]) + Double.parseDouble(parts[1]);
        } else if (expression.contains("-")) {
            String[] parts = expression.split("-");
            return Double.parseDouble(parts[0]) - Double.parseDouble(parts[1]);
        } else if (expression.contains("*")) {
            String[] parts = expression.split("\\*");
            return Double.parseDouble(parts[0]) * Double.parseDouble(parts[1]);
        } else if (expression.contains("/")) {
            String[] parts = expression.split("/");
            return Double.parseDouble(parts[0]) / Double.parseDouble(parts[1]);
        } else {
            return Double.parseDouble(expression);
        }
    }
    
    private Map<String, Object> createSearchParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("type", "string");
        queryParam.put("description", "搜索查询词");
        properties.put("query", queryParam);
        
        parameters.put("properties", properties);
        parameters.put("required", List.of("query"));
        
        return parameters;
    }
    
    private Map<String, Object> createCalculatorParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> expressionParam = new HashMap<>();
        expressionParam.put("type", "string");
        expressionParam.put("description", "数学表达式");
        properties.put("expression", expressionParam);
        
        parameters.put("properties", properties);
        parameters.put("required", List.of("expression"));
        
        return parameters;
    }
    
    private Map<String, Object> createMcpParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        
        Map<String, Object> serviceParam = new HashMap<>();
        serviceParam.put("type", "string");
        serviceParam.put("description", "MCP服务名称");
        properties.put("service", serviceParam);
        
        Map<String, Object> methodParam = new HashMap<>();
        methodParam.put("type", "string");
        methodParam.put("description", "调用方法");
        properties.put("method", methodParam);
        
        Map<String, Object> argsParam = new HashMap<>();
        argsParam.put("type", "object");
        argsParam.put("description", "方法参数");
        properties.put("args", argsParam);
        
        parameters.put("properties", properties);
        parameters.put("required", List.of("service", "method"));
        
        return parameters;
    }
    
    private static class ToolDefinition {
        String name;
        String description;
        Map<String, Object> parameters;
    }
}
