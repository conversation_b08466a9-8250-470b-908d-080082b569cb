package com.agent.controller;

import com.agent.domain.dto.AgentExecutionRequest;
import com.agent.domain.dto.AgentExecutionResponse;
import com.agent.service.agent.AgentService;
import com.agent.service.llm.LLMService;
import com.agent.domain.dto.ChatRequest;
import com.agent.domain.dto.ChatResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * 后端链路测试控制器
 */
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Slf4j
public class TestController {

    private final AgentService agentService;
    private final LLMService llmService;
    private final ObjectMapper objectMapper;

    /**
     * 测试1: 简单的流式数据返回
     */
    @GetMapping(value = "/stream/simple", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> testSimpleStream() {
        log.info("开始测试简单流式数据");
        
        return Flux.just("Hello", " ", "World", "!", " ", "This", " ", "is", " ", "a", " ", "test", ".")
                .delayElements(Duration.ofMillis(200))
                .map(word -> {
                    try {
                        AgentExecutionResponse response = AgentExecutionResponse.builder()
                                .content(word)
                                .finished(false)
                                .isStream(true)
                                .build();
                        String json = objectMapper.writeValueAsString(response);
                        log.debug("发送简单流式数据: {}", word);
                        return json;
                    } catch (Exception e) {
                        log.error("序列化失败", e);
                        return "{\"error\":\"序列化失败\"}";
                    }
                })
                .concatWith(Flux.just("[DONE]"));
    }

    /**
     * 测试2: 阿里云LLM服务直接测试
     */
    @PostMapping(value = "/stream/aliyun", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> testAliyunStream(@RequestBody TestChatRequest request) {
        log.info("开始测试阿里云LLM流式服务: {}", request.getMessage());

        // 构建聊天请求
        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setModel("qwen-turbo");
        chatRequest.setTemperature(0.7);
        chatRequest.setMaxTokens(1000);
        chatRequest.setStream(true);

        // 构建消息列表
        List<ChatRequest.Message> messages = new ArrayList<>();

        ChatRequest.Message systemMessage = new ChatRequest.Message();
        systemMessage.setRole("system");
        systemMessage.setContent("你是一个友好的AI助手。");
        messages.add(systemMessage);

        ChatRequest.Message userMessage = new ChatRequest.Message();
        userMessage.setRole("user");
        userMessage.setContent(request.getMessage());
        messages.add(userMessage);

        chatRequest.setMessages(messages);

        return llmService.chatStream(chatRequest)
                .doOnNext(chatResponse -> log.debug("收到阿里云响应: content='{}', finished={}",
                        chatResponse.getContent(), chatResponse.getFinishReason() != null))
                .map(chatResponse -> {
                    try {
                        AgentExecutionResponse response = new AgentExecutionResponse();
                        response.setContent(chatResponse.getContent() != null ? chatResponse.getContent() : "");
                        response.setFinished(chatResponse.getFinishReason() != null);
                        response.setIsStream(true);

                        String json = objectMapper.writeValueAsString(response);
                        log.debug("转换为Agent响应: content='{}'", response.getContent());
                        return json;
                    } catch (Exception e) {
                        log.error("序列化失败", e);
                        return "{\"error\":\"序列化失败\"}";
                    }
                })
                .concatWith(Flux.just("[DONE]"));
    }

    /**
     * 测试3: 完整的Agent执行链路测试 (简化版本，直接测试原始API)
     */
    @PostMapping(value = "/stream/agent", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> testAgentStream(@RequestBody TestAgentRequest request) {
        log.info("开始测试完整Agent执行链路: agentId={}, query={}", request.getAgentId(), request.getQuery());

        // 直接调用原始的Agent执行API
        return Flux.just("测试Agent链路暂时跳过，因为需要完整的Agent上下文")
                .map(message -> {
                    try {
                        AgentExecutionResponse response = new AgentExecutionResponse();
                        response.setContent(message);
                        response.setFinished(true);
                        response.setIsStream(true);
                        return objectMapper.writeValueAsString(response);
                    } catch (Exception e) {
                        log.error("序列化失败", e);
                        return "{\"error\":\"序列化失败\"}";
                    }
                })
                .concatWith(Flux.just("[DONE]"));
    }

    /**
     * 测试4: 原始阿里云API响应测试
     */
    @PostMapping(value = "/stream/raw", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> testRawAliyunStream(@RequestBody TestChatRequest request) {
        log.info("开始测试原始阿里云API响应: {}", request.getMessage());

        ChatRequest chatRequest = new ChatRequest();
        chatRequest.setModel("qwen-turbo");
        chatRequest.setTemperature(0.7);
        chatRequest.setMaxTokens(500);
        chatRequest.setStream(true);

        List<ChatRequest.Message> messages = new ArrayList<>();
        ChatRequest.Message userMessage = new ChatRequest.Message();
        userMessage.setRole("user");
        userMessage.setContent(request.getMessage());
        messages.add(userMessage);
        chatRequest.setMessages(messages);

        return llmService.chatStream(chatRequest)
                .doOnNext(chatResponse -> log.info("原始阿里云响应: {}", chatResponse))
                .map(chatResponse -> {
                    try {
                        // 直接返回原始响应的JSON
                        return objectMapper.writeValueAsString(chatResponse);
                    } catch (Exception e) {
                        log.error("序列化原始响应失败", e);
                        return "{\"error\":\"序列化失败\"}";
                    }
                })
                .concatWith(Flux.just("[DONE]"));
    }

    /**
     * 测试请求对象
     */
    public static class TestChatRequest {
        private String message;
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class TestAgentRequest {
        private Long agentId;
        private String query;
        private Long userId = 1L;
        
        public Long getAgentId() { return agentId; }
        public void setAgentId(Long agentId) { this.agentId = agentId; }
        public String getQuery() { return query; }
        public void setQuery(String query) { this.query = query; }
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
    }
}
