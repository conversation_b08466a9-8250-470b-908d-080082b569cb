package com.agent.repository;

import com.agent.domain.entity.AgentMcpServer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Agent MCP Server关联 Repository
 */
@Repository
public interface AgentMcpServerRepository extends JpaRepository<AgentMcpServer, Long> {
    
    /**
     * 根据Agent ID查找关联的MCP Server
     */
    @Query("SELECT ams FROM AgentMcpServer ams " +
           "JOIN FETCH ams.mcpServer ms " +
           "WHERE ams.agentId = :agentId " +
           "ORDER BY ams.priority ASC, ams.createdAt ASC")
    List<AgentMcpServer> findByAgentIdWithMcpServer(@Param("agentId") Long agentId);
    
    /**
     * 根据Agent ID查找启用的MCP Server
     */
    @Query("SELECT ams FROM AgentMcpServer ams " +
           "JOIN FETCH ams.mcpServer ms " +
           "WHERE ams.agentId = :agentId AND ams.enabled = true " +
           "ORDER BY ams.priority ASC, ams.createdAt ASC")
    List<AgentMcpServer> findEnabledByAgentIdWithMcpServer(@Param("agentId") Long agentId);
    
    /**
     * 删除Agent的所有MCP Server关联
     */
    void deleteByAgentId(Long agentId);
    
    /**
     * 查找特定Agent和MCP Server的关联
     */
    AgentMcpServer findByAgentIdAndMcpServerId(Long agentId, Long mcpServerId);
}
