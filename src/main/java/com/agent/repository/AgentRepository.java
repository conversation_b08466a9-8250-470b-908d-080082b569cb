package com.agent.repository;

import com.agent.domain.entity.Agent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Agent数据访问接口
 */
@Repository
public interface AgentRepository extends JpaRepository<Agent, Long> {
    
    /**
     * 根据创建者ID查找Agent列表，按创建时间倒序
     */
    List<Agent> findByCreatorIdOrderByCreatedAtDesc(Long creatorId);
    
    /**
     * 根据创建者ID和状态查找Agent列表
     */
    List<Agent> findByCreatorIdAndStatus(Long creatorId, Agent.AgentStatus status);
    
    /**
     * 根据模式查找Agent列表
     */
    List<Agent> findByMode(Agent.AgentMode mode);
    
    /**
     * 根据名称模糊查询Agent
     */
    List<Agent> findByNameContainingIgnoreCase(String name);
    
    /**
     * 查找已发布的Agent
     */
    List<Agent> findByStatusOrderByCreatedAtDesc(Agent.AgentStatus status);
    
    /**
     * 统计用户的Agent数量
     */
    @Query("SELECT COUNT(a) FROM Agent a WHERE a.creatorId = :creatorId")
    Long countByCreatorId(@Param("creatorId") Long creatorId);
    
    /**
     * 统计各状态的Agent数量
     */
    @Query("SELECT a.status, COUNT(a) FROM Agent a WHERE a.creatorId = :creatorId GROUP BY a.status")
    List<Object[]> countByCreatorIdGroupByStatus(@Param("creatorId") Long creatorId);
}
