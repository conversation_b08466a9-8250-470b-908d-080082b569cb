package com.agent.domain.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档实体
 */
@Entity
@Table(name = "documents")
@Data
@EqualsAndHashCode(callSuper = false)
public class Document {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 文档UUID
     */
    @Column(nullable = false, unique = true, length = 36)
    private String uuid;
    
    /**
     * 关联的知识库
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "knowledge_base_id", nullable = false)
    private KnowledgeBase knowledgeBase;
    
    /**
     * 文档名称
     */
    @Column(nullable = false, length = 200)
    private String name;
    
    /**
     * 文档类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DocumentType type;
    
    /**
     * 文件路径或URL
     */
    @Column(name = "file_path", length = 500)
    private String filePath;
    
    /**
     * 文件大小 (字节)
     */
    @Column(name = "file_size")
    private Long fileSize;
    
    /**
     * MIME类型
     */
    @Column(name = "mime_type", length = 100)
    private String mimeType;
    
    /**
     * 文档内容 (对于小文档直接存储)
     */
    @Column(columnDefinition = "TEXT")
    private String content;
    
    /**
     * 文档摘要
     */
    @Column(columnDefinition = "TEXT")
    private String summary;
    
    /**
     * 处理状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ProcessingStatus status = ProcessingStatus.PENDING;
    
    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;
    
    /**
     * 字符数
     */
    @Column(name = "character_count")
    private Integer characterCount = 0;
    
    /**
     * 分块数量
     */
    @Column(name = "chunk_count")
    private Integer chunkCount = 0;
    
    /**
     * 上传者ID
     */
    @Column(name = "uploader_id", nullable = false)
    private Long uploaderId;
    
    // 文档分块功能暂时注释，后续实现
    // @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    // private List<DocumentChunk> chunks;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 文档类型枚举
     */
    public enum DocumentType {
        TEXT("文本"),
        PDF("PDF"),
        WORD("Word文档"),
        EXCEL("Excel表格"),
        POWERPOINT("PowerPoint演示文稿"),
        MARKDOWN("Markdown"),
        HTML("HTML"),
        CSV("CSV"),
        JSON("JSON"),
        XML("XML"),
        OTHER("其他");
        
        private final String description;
        
        DocumentType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 处理状态枚举
     */
    public enum ProcessingStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        ERROR("错误"),
        CANCELLED("已取消");
        
        private final String description;
        
        ProcessingStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
