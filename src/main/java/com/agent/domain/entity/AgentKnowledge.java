package com.agent.domain.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * Agent知识库关联实体
 */
@Entity
@Table(name = "agent_knowledge")
@Data
@EqualsAndHashCode(callSuper = false)
public class AgentKnowledge {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的Agent
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", nullable = false)
    private Agent agent;
    
    /**
     * 关联的知识库
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "knowledge_base_id", nullable = false)
    private KnowledgeBase knowledgeBase;
    
    /**
     * 检索配置
     */
    @Embedded
    private RetrievalConfig retrievalConfig;
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 检索配置
     */
    @Embeddable
    @Data
    public static class RetrievalConfig {
        
        /**
         * 检索模式: SEMANTIC(语义检索) 或 KEYWORD(关键词检索)
         */
        @Enumerated(EnumType.STRING)
        @Column(name = "retrieval_mode")
        private RetrievalMode retrievalMode = RetrievalMode.SEMANTIC;
        
        /**
         * 相似度阈值 (0.0-1.0)
         */
        @Column(name = "similarity_threshold")
        private Double similarityThreshold = 0.7;
        
        /**
         * 最大检索数量
         */
        @Column(name = "max_results")
        private Integer maxResults = 5;
        
        /**
         * 是否启用重排序
         */
        @Column(name = "rerank_enabled")
        private Boolean rerankEnabled = false;
        
        public enum RetrievalMode {
            SEMANTIC("语义检索"),
            KEYWORD("关键词检索"),
            HYBRID("混合检索");
            
            private final String description;
            
            RetrievalMode(String description) {
                this.description = description;
            }
            
            public String getDescription() {
                return description;
            }
        }
    }
}
