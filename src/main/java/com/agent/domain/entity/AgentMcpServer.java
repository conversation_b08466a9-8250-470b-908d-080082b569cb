package com.agent.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent与MCP Server关联实体
 */
@Entity
@Table(name = "agent_mcp_servers")
@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class AgentMcpServer {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * Agent ID
     */
    @Column(name = "agent_id", nullable = false)
    private Long agentId;
    
    /**
     * MCP Server ID
     */
    @Column(name = "mcp_server_id", nullable = false)
    private Long mcpServerId;
    
    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;
    
    /**
     * 优先级 (数字越小优先级越高)
     */
    @Column(nullable = false)
    private Integer priority = 0;
    
    /**
     * 配置覆盖 (JSON格式存储，用于覆盖MCP Server的默认配置)
     */
    @Column(name = "config_override", columnDefinition = "TEXT")
    private String configOverride;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Agent实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agent_id", insertable = false, updatable = false)
    private Agent agent;
    
    /**
     * MCP Server实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mcp_server_id", insertable = false, updatable = false)
    private McpServer mcpServer;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
