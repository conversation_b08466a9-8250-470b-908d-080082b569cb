package com.agent.domain.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库实体
 */
@Entity
@Table(name = "knowledge_bases")
@Data
@EqualsAndHashCode(callSuper = false)
public class KnowledgeBase {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(length = 500)
    private String description;
    
    /**
     * 知识库类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private KnowledgeType type;
    
    /**
     * 向量化配置
     */
    @Embedded
    private VectorConfig vectorConfig;
    
    /**
     * 知识库状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private KnowledgeStatus status = KnowledgeStatus.BUILDING;
    
    /**
     * 创建者ID
     */
    @Column(nullable = false)
    private Long creatorId;
    
    /**
     * 文档数量
     */
    @Column(name = "document_count")
    private Integer documentCount = 0;
    
    /**
     * 向量数量
     */
    @Column(name = "vector_count")
    private Integer vectorCount = 0;
    
    /**
     * 关联的文档
     */
    @OneToMany(mappedBy = "knowledgeBase", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Document> documents;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 知识库类型枚举
     */
    public enum KnowledgeType {
        DOCUMENT("文档知识库"),
        QA("问答知识库"),
        WEB("网页知识库"),
        API("API知识库");
        
        private final String description;
        
        KnowledgeType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 知识库状态枚举
     */
    public enum KnowledgeStatus {
        BUILDING("构建中"),
        READY("就绪"),
        ERROR("错误"),
        UPDATING("更新中");
        
        private final String description;
        
        KnowledgeStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 向量化配置
     */
    @Embeddable
    @Data
    public static class VectorConfig {
        
        /**
         * 嵌入模型名称
         */
        @Column(name = "embedding_model")
        private String embeddingModel = "text-embedding-v1";
        
        /**
         * 向量维度
         */
        @Column(name = "vector_dimension")
        private Integer vectorDimension = 1536;
        
        /**
         * 分块大小
         */
        @Column(name = "chunk_size")
        private Integer chunkSize = 1000;
        
        /**
         * 分块重叠
         */
        @Column(name = "chunk_overlap")
        private Integer chunkOverlap = 200;
        
        /**
         * 分块策略
         */
        @Enumerated(EnumType.STRING)
        @Column(name = "chunk_strategy")
        private ChunkStrategy chunkStrategy = ChunkStrategy.RECURSIVE;
        
        public enum ChunkStrategy {
            RECURSIVE("递归分块"),
            SENTENCE("句子分块"),
            PARAGRAPH("段落分块"),
            SEMANTIC("语义分块");
            
            private final String description;
            
            ChunkStrategy(String description) {
                this.description = description;
            }
            
            public String getDescription() {
                return description;
            }
        }
    }
}
