package com.agent.domain.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 聊天请求DTO
 */
@Data
public class ChatRequest {
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 消息列表
     */
    private List<Message> messages;
    
    /**
     * 温度参数 (0.0-2.0)
     */
    private Double temperature;
    
    /**
     * 最大输出token数
     */
    private Integer maxTokens;
    
    /**
     * Top-p参数 (0.0-1.0)
     */
    private Double topP;
    
    /**
     * 频率惩罚 (-2.0-2.0)
     */
    private Double frequencyPenalty;
    
    /**
     * 存在惩罚 (-2.0-2.0)
     */
    private Double presencePenalty;
    
    /**
     * 停止词列表
     */
    private List<String> stop;
    
    /**
     * 工具列表
     */
    private List<Tool> tools;
    
    /**
     * 是否流式输出
     */
    private Boolean stream = false;
    
    /**
     * 消息
     */
    @Data
    public static class Message {
        /**
         * 角色: system, user, assistant, tool
         */
        private String role;
        
        /**
         * 内容
         */
        private String content;
        
        /**
         * 工具调用ID (仅当role为tool时使用)
         */
        private String toolCallId;
        
        /**
         * 工具调用列表 (仅当role为assistant时使用)
         */
        private List<ToolCall> toolCalls;
    }
    
    /**
     * 工具调用
     */
    @Data
    public static class ToolCall {
        /**
         * 调用ID
         */
        private String id;
        
        /**
         * 类型 (通常为 "function")
         */
        private String type;
        
        /**
         * 函数调用信息
         */
        private Function function;
        
        @Data
        public static class Function {
            /**
             * 函数名称
             */
            private String name;
            
            /**
             * 函数参数 (JSON字符串)
             */
            private String arguments;
        }
    }
    
    /**
     * 工具定义
     */
    @Data
    public static class Tool {
        /**
         * 类型 (通常为 "function")
         */
        private String type;
        
        /**
         * 函数定义
         */
        private Function function;
        
        @Data
        public static class Function {
            /**
             * 函数名称
             */
            private String name;
            
            /**
             * 函数描述
             */
            private String description;
            
            /**
             * 参数定义 (JSON Schema)
             */
            private Map<String, Object> parameters;
        }
    }
}
