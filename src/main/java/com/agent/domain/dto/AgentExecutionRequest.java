package com.agent.domain.dto;

import com.agent.domain.entity.Agent;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Agent执行请求DTO
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AgentExecutionRequest {
    
    /**
     * Agent实体
     */
    private Agent agent;
    
    /**
     * 用户查询
     */
    private String query;
    
    /**
     * 会话ID
     */
    private String conversationId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 是否流式输出
     */
    @Builder.Default
    private Boolean stream = false;
    
    /**
     * 额外的上下文信息
     */
    private Map<String, Object> context;
    
    /**
     * 最大执行时间(秒)
     */
    @Builder.Default
    private Integer maxExecutionTime = 300;
}
