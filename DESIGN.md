# Agent V3 系统设计文档

## 1. 系统概述

Agent V3 是一个基于阿里云通义千问的智能Agent平台，支持两种执行模式：
- **ReAct模式**: 基于推理-行动循环的Agent执行模式
- **Function Calling模式**: 基于函数调用的Agent执行模式

## 2. 技术架构

### 2.1 技术栈
- **后端框架**: Spring Boot 3.2.0 + Java 17
- **数据库**: PostgreSQL (主数据库)
- **缓存**: Redis
- **大模型**: 阿里云通义千问 (DashScope API)
- **前端**: React + TypeScript (待实现)

### 2.2 系统分层

```
┌─────────────────────────────────────────┐
│              前端层 (React)              │
├─────────────────────────────────────────┤
│              REST API层                  │
│  ┌─────────────────────────────────────┐ │
│  │        AgentController              │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│              业务服务层                   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ AgentService│  │ LLMService      │   │
│  │ ToolService │  │ KnowledgeService│   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│              执行引擎层                   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ ReactAgent  │  │ FunctionCalling │   │
│  │ Executor    │  │ AgentExecutor   │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│              数据访问层                   │
│  ┌─────────────────────────────────────┐ │
│  │        PostgreSQL + JPA             │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 3. 核心模块设计

### 3.1 Agent实体模型

```java
Agent {
    - id: Long
    - name: String
    - description: String
    - mode: AgentMode (REACT | FUNCTION_CALLING)
    - systemPrompt: String
    - openingStatement: String
    - modelConfig: ModelConfig
    - status: AgentStatus
    - knowledgeBases: List<AgentKnowledge>
    - tools: List<AgentTool>
}
```

### 3.2 执行引擎设计

#### 3.2.1 ReAct模式执行流程
```
用户输入 → 构建ReAct提示词 → LLM推理 → 解析Action → 执行Action → 获取Observation → 继续推理 → 最终答案
```

#### 3.2.2 Function Calling模式执行流程
```
用户输入 → 构建函数列表 → LLM决策 → 执行函数调用 → 获取结果 → LLM生成最终回答
```

### 3.3 大模型服务集成

- **同步调用**: 适用于简单查询
- **流式调用**: 适用于长文本生成
- **函数调用**: 支持工具调用
- **嵌入向量**: 支持知识库检索

## 4. 数据库设计

### 4.1 核心表结构

```sql
-- Agent表
agents (
    id, name, description, mode, system_prompt, 
    opening_statement, model_config, status, 
    creator_id, created_at, updated_at
)

-- 知识库表
knowledge_bases (
    id, name, description, type, vector_config,
    status, creator_id, document_count, vector_count
)

-- 工具表
tools (
    id, name, description, type, category,
    parameter_schema, implementation_class, api_config
)

-- 会话表
conversations (
    id, uuid, agent_id, user_id, title,
    status, summary, message_count, last_active_at
)

-- 消息表
messages (
    id, uuid, conversation_id, role, content,
    type, tool_calls, tool_results, status, token_usage
)
```

## 5. API设计

### 5.1 Agent管理API

```
POST   /v1/agents              # 创建Agent
GET    /v1/agents              # 获取Agent列表
GET    /v1/agents/{id}         # 获取Agent详情
PUT    /v1/agents/{id}         # 更新Agent
DELETE /v1/agents/{id}         # 删除Agent
POST   /v1/agents/{id}/publish # 发布Agent
POST   /v1/agents/{id}/archive # 归档Agent
POST   /v1/agents/{id}/clone   # 复制Agent
```

### 5.2 Agent执行API

```
POST /v1/agents/{id}/execute        # 同步执行
POST /v1/agents/{id}/execute/stream # 流式执行
POST /v1/agents/{id}/validate       # 验证配置
```

## 6. 配置说明

### 6.1 应用配置 (application.yml)

```yaml
# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:agent_user}
    password: ${DB_PASSWORD:agent_pass}

# 阿里云配置
aliyun:
  dashscope:
    api-key: ${ALIYUN_API_KEY:your-api-key}
    base-url: https://dashscope.aliyuncs.com/api/v1
    model:
      chat: qwen-turbo
      embedding: text-embedding-v1
```

## 7. 部署架构

### 7.1 开发环境
```
Application Server (Spring Boot)
    ↓
PostgreSQL Database
    ↓
Redis Cache
    ↓
Aliyun DashScope API
```

### 7.2 生产环境建议
```
Load Balancer
    ↓
Application Servers (多实例)
    ↓
PostgreSQL Cluster (主从)
    ↓
Redis Cluster
    ↓
Aliyun DashScope API
```

## 8. 扩展性设计

### 8.1 新增Agent模式
1. 实现 `AgentExecutor` 接口
2. 注册到Spring容器
3. 系统自动识别并支持

### 8.2 新增工具类型
1. 实现工具接口
2. 定义参数Schema
3. 注册到工具服务

### 8.3 新增大模型支持
1. 实现 `LLMService` 接口
2. 配置模型参数
3. 切换服务实现

## 9. 安全考虑

- API密钥安全存储
- 用户权限控制
- 输入参数验证
- SQL注入防护
- XSS攻击防护

## 10. 监控与日志

- 应用性能监控 (APM)
- 数据库性能监控
- API调用统计
- 错误日志收集
- 用户行为分析

## 11. 下一步开发计划

1. **完善基础服务**: 实现ToolService和KnowledgeService
2. **前端界面开发**: React组件和页面
3. **测试用例编写**: 单元测试和集成测试
4. **性能优化**: 缓存策略和数据库优化
5. **部署脚本**: Docker化和CI/CD流程
