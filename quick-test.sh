#!/bin/bash

echo "🧪 快速测试 Agent V3 项目..."

# 只运行编译，不运行测试
echo "编译项目..."
mvn clean compile -DskipTests

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功！"

# 检查数据库连接
echo "检查数据库连接..."
if command -v psql &> /dev/null; then
    psql -h 127.0.0.1 -p 5432 -U yilin -d agent_v2 -c "SELECT 1;" &> /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 数据库连接正常"
    else
        echo "⚠️  数据库连接失败，将使用模拟模式"
    fi
else
    echo "⚠️  未找到psql命令，将使用模拟模式"
fi

echo ""
echo "🚀 启动应用..."
echo "📱 前端页面将在: http://localhost:8080"
echo "🔧 API文档: http://localhost:8080/v1/agents"
echo ""
echo "按 Ctrl+C 停止应用"
echo ""

# 启动应用
mvn spring-boot:run -DskipTests
