# Agent V3 - 智能Agent平台

基于阿里云通义千问的智能Agent平台，支持ReAct和Function Calling两种执行模式。

## 🚀 功能特性

- **双模式支持**: ReAct推理模式 + Function Calling函数调用模式
- **阿里云集成**: 基于通义千问大模型服务
- **知识库支持**: 向量化知识检索
- **工具生态**: 可扩展的工具调用系统
- **流式输出**: 支持实时流式响应
- **现代化UI**: React + Ant Design界面

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.2.0
- **语言**: Java 17
- **数据库**: PostgreSQL
- **缓存**: Redis
- **大模型**: 阿里云通义千问 (DashScope)

### 前端技术栈
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design 5.x
- **状态管理**: React Hooks
- **HTTP客户端**: Axios

## 📦 快速开始

### 环境要求

- Java 17+
- Node.js 16+
- PostgreSQL 12+
- Redis 6+
- Maven 3.6+

### 1. 克隆项目

```bash
git clone <repository-url>
cd agent-v3
```

### 2. 配置数据库

创建PostgreSQL数据库：

```sql
CREATE DATABASE agent_db;
CREATE USER agent_user WITH PASSWORD 'agent_pass';
GRANT ALL PRIVILEGES ON DATABASE agent_db TO agent_user;
```

### 3. 配置环境变量

创建 `.env` 文件：

```bash
# 数据库配置
DB_USERNAME=agent_user
DB_PASSWORD=agent_pass

# 阿里云API密钥
ALIYUN_API_KEY=your-dashscope-api-key

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

### 4. 启动后端服务

```bash
# 安装依赖并启动
mvn clean install
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动。

### 5. 启动前端服务

```bash
cd frontend
npm install
npm start
```

前端服务将在 `http://localhost:3000` 启动。

## 🔧 配置说明

### 应用配置 (application.yml)

```yaml
# 服务端口
server:
  port: 8080

# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:agent_user}
    password: ${DB_PASSWORD:agent_pass}

# 阿里云配置
aliyun:
  dashscope:
    api-key: ${ALIYUN_API_KEY:your-api-key}
    model:
      chat: qwen-turbo
      embedding: text-embedding-v1
```

### 获取阿里云API密钥

1. 访问 [阿里云DashScope控制台](https://dashscope.console.aliyun.com/)
2. 创建应用并获取API Key
3. 将API Key配置到环境变量中

## 📚 API文档

### Agent管理API

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/v1/agents` | 创建Agent |
| GET | `/v1/agents` | 获取Agent列表 |
| GET | `/v1/agents/{id}` | 获取Agent详情 |
| PUT | `/v1/agents/{id}` | 更新Agent |
| DELETE | `/v1/agents/{id}` | 删除Agent |
| POST | `/v1/agents/{id}/publish` | 发布Agent |
| POST | `/v1/agents/{id}/execute` | 执行Agent |
| POST | `/v1/agents/{id}/execute/stream` | 流式执行Agent |

### 请求示例

创建Agent：

```bash
curl -X POST http://localhost:8080/api/v1/agents \
  -H "Content-Type: application/json" \
  -d '{
    "name": "智能助手",
    "description": "一个通用的智能助手",
    "mode": "FUNCTION_CALLING",
    "systemPrompt": "你是一个智能助手，可以帮助用户解答问题。",
    "modelConfig": {
      "modelName": "qwen-turbo",
      "temperature": 0.7,
      "maxTokens": 2000
    },
    "creatorId": 1
  }'
```

执行Agent：

```bash
curl -X POST http://localhost:8080/api/v1/agents/1/execute \
  -H "Content-Type: application/json" \
  -d '{
    "query": "你好，请介绍一下自己",
    "userId": 1
  }'
```

## 🎯 使用指南

### 1. 创建Agent

1. 访问前端界面
2. 点击"创建Agent"
3. 填写基本信息：
   - Agent名称
   - 描述
   - 执行模式 (ReAct/Function Calling)
   - 系统提示词
4. 配置模型参数
5. 添加知识库和工具
6. 保存并测试

### 2. ReAct模式配置

ReAct模式适用于需要多步推理的复杂任务：

```
系统提示词示例：
你是一个智能助手，使用ReAct方法来回答问题。
请按照 Thought -> Action -> Observation 的格式进行推理。
可用工具：搜索、计算器、知识库查询
```

### 3. Function Calling模式配置

Function Calling模式适用于明确的工具调用场景：

```
系统提示词示例：
你是一个智能助手，可以调用各种工具来帮助用户。
根据用户需求选择合适的工具，并解释结果。
```

## 🔍 开发指南

### 添加新的Agent执行模式

1. 实现 `AgentExecutor` 接口：

```java
@Service
public class CustomAgentExecutor implements AgentExecutor {
    @Override
    public Agent.AgentMode getSupportedMode() {
        return Agent.AgentMode.CUSTOM;
    }
    
    @Override
    public Mono<AgentExecutionResponse> execute(AgentExecutionRequest request) {
        // 实现执行逻辑
    }
}
```

2. 系统会自动识别并注册新的执行器

### 添加新工具

1. 实现工具接口
2. 定义参数Schema
3. 注册到工具服务

### 扩展知识库类型

1. 实现知识库处理器
2. 添加向量化逻辑
3. 配置检索策略

## 🧪 测试

### 运行单元测试

```bash
mvn test
```

### 运行集成测试

```bash
mvn verify
```

### 前端测试

```bash
cd frontend
npm test
```

## 📈 监控与日志

- 应用日志：`logs/agent-v3.log`
- 访问日志：通过Spring Boot Actuator
- 性能监控：集成Micrometer
- 健康检查：`/actuator/health`

## 🚀 部署

### Docker部署

```bash
# 构建镜像
docker build -t agent-v3 .

# 运行容器
docker run -d \
  -p 8080:8080 \
  -e ALIYUN_API_KEY=your-api-key \
  -e DB_USERNAME=agent_user \
  -e DB_PASSWORD=agent_pass \
  agent-v3
```

### 生产环境部署

1. 配置负载均衡器
2. 设置数据库集群
3. 配置Redis集群
4. 设置监控和日志收集
5. 配置SSL证书

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交Issue或联系开发团队。
