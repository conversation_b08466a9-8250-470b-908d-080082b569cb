import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  Divider,
  Row,
  Col,
  Switch,
  Slider,
  InputNumber,
  message,
  Tabs,
  List,
  Tag,
  Modal,
  Upload
} from 'antd';
import {
  SaveOutlined,
  PlayCircleOutlined,
  SettingOutlined,
  BookOutlined,
  ToolOutlined,
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { Agent, AgentMode, ModelConfig } from '../types/agent';
import { agentService } from '../services/agentService';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface AgentConfigProps {
  agentId?: number;
  onSave?: (agent: Agent) => void;
}

const AgentConfig: React.FC<AgentConfigProps> = ({ agentId, onSave }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [agent, setAgent] = useState<Agent | null>(null);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (agentId) {
      loadAgent(agentId);
    }
  }, [agentId]);

  const loadAgent = async (id: number) => {
    try {
      setLoading(true);
      const response = await agentService.getAgent(id);
      setAgent(response.data);
      form.setFieldsValue(response.data);
    } catch (error) {
      message.error('加载Agent失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: any) => {
    try {
      setLoading(true);
      const agentData = {
        ...values,
        id: agentId
      };

      let response;
      if (agentId) {
        response = await agentService.updateAgent(agentId, agentData);
      } else {
        response = await agentService.createAgent(agentData);
      }

      message.success(agentId ? 'Agent更新成功' : 'Agent创建成功');
      setAgent(response.data);
      onSave?.(response.data);
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    try {
      setLoading(true);
      // 这里可以添加测试Agent的逻辑
      message.success('测试完成');
    } catch (error) {
      message.error('测试失败');
    } finally {
      setLoading(false);
    }
  };

  const renderBasicConfig = () => (
    <Card title="基础配置" size="small">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="name"
            label="Agent名称"
            rules={[{ required: true, message: '请输入Agent名称' }]}
          >
            <Input placeholder="请输入Agent名称" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="mode"
            label="执行模式"
            rules={[{ required: true, message: '请选择执行模式' }]}
          >
            <Select placeholder="请选择执行模式">
              <Option value="REACT">ReAct模式</Option>
              <Option value="FUNCTION_CALLING">Function Calling模式</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="description"
        label="描述"
      >
        <TextArea rows={3} placeholder="请输入Agent描述" />
      </Form.Item>

      <Form.Item
        name="systemPrompt"
        label="系统提示词"
        rules={[{ required: true, message: '请输入系统提示词' }]}
      >
        <TextArea
          rows={8}
          placeholder="请输入系统提示词，这将决定Agent的行为和能力"
        />
      </Form.Item>

      <Form.Item
        name="openingStatement"
        label="开场白"
      >
        <TextArea
          rows={3}
          placeholder="请输入开场白，用户开始对话时显示"
        />
      </Form.Item>
    </Card>
  );

  const renderModelConfig = () => (
    <Card title="模型配置" size="small">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['modelConfig', 'modelName']}
            label="模型名称"
            initialValue="qwen-turbo"
          >
            <Select>
              <Option value="qwen-turbo">通义千问-Turbo</Option>
              <Option value="qwen-plus">通义千问-Plus</Option>
              <Option value="qwen-max">通义千问-Max</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['modelConfig', 'maxTokens']}
            label="最大Token数"
            initialValue={2000}
          >
            <InputNumber min={1} max={8000} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['modelConfig', 'temperature']}
            label="温度参数"
            initialValue={0.7}
          >
            <Slider
              min={0}
              max={2}
              step={0.1}
              marks={{
                0: '0',
                0.5: '0.5',
                1: '1',
                1.5: '1.5',
                2: '2'
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['modelConfig', 'topP']}
            label="Top-P参数"
            initialValue={0.9}
          >
            <Slider
              min={0}
              max={1}
              step={0.1}
              marks={{
                0: '0',
                0.5: '0.5',
                1: '1'
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name={['modelConfig', 'streamEnabled']}
            label="启用流式输出"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const renderKnowledgeConfig = () => (
    <Card title="知识库配置" size="small">
      <div style={{ marginBottom: 16 }}>
        <Button type="dashed" icon={<PlusOutlined />} block>
          添加知识库
        </Button>
      </div>
      
      <List
        dataSource={[]}
        locale={{ emptyText: '暂无知识库' }}
        renderItem={(item: any) => (
          <List.Item
            actions={[
              <Button type="link" size="small">配置</Button>,
              <Button type="link" size="small" danger>删除</Button>
            ]}
          >
            <List.Item.Meta
              title={item.name}
              description={item.description}
            />
            <Tag color="blue">{item.type}</Tag>
          </List.Item>
        )}
      />
    </Card>
  );

  const renderToolsConfig = () => (
    <Card title="工具配置" size="small">
      <div style={{ marginBottom: 16 }}>
        <Button type="dashed" icon={<PlusOutlined />} block>
          添加工具
        </Button>
      </div>
      
      <List
        dataSource={[]}
        locale={{ emptyText: '暂无工具' }}
        renderItem={(item: any) => (
          <List.Item
            actions={[
              <Switch size="small" defaultChecked />,
              <Button type="link" size="small">配置</Button>,
              <Button type="link" size="small" danger>删除</Button>
            ]}
          >
            <List.Item.Meta
              title={item.name}
              description={item.description}
            />
            <Tag color="green">{item.category}</Tag>
          </List.Item>
        )}
      />
    </Card>
  );

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={loading}
            onClick={() => form.submit()}
          >
            保存
          </Button>
          <Button
            icon={<PlayCircleOutlined />}
            loading={loading}
            onClick={handleTest}
          >
            测试
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                基础配置
              </span>
            }
            key="basic"
          >
            {renderBasicConfig()}
            <div style={{ marginTop: 16 }}>
              {renderModelConfig()}
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BookOutlined />
                知识库
              </span>
            }
            key="knowledge"
          >
            {renderKnowledgeConfig()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <ToolOutlined />
                工具
              </span>
            }
            key="tools"
          >
            {renderToolsConfig()}
          </TabPane>
        </Tabs>
      </Form>
    </div>
  );
};

export default AgentConfig;
