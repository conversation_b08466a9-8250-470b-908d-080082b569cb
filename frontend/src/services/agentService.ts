import axios from 'axios';
import { Agent, AgentExecutionRequest, AgentExecutionResponse } from '../types/agent';

const API_BASE_URL = '/api/v1';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 处理未授权错误
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const agentService = {
  // 创建Agent
  createAgent: (agent: Omit<Agent, 'id'>) => {
    return apiClient.post<Agent>('/agents', agent);
  },

  // 更新Agent
  updateAgent: (id: number, agent: Partial<Agent>) => {
    return apiClient.put<Agent>(`/agents/${id}`, agent);
  },

  // 删除Agent
  deleteAgent: (id: number) => {
    return apiClient.delete(`/agents/${id}`);
  },

  // 获取Agent详情
  getAgent: (id: number) => {
    return apiClient.get<Agent>(`/agents/${id}`);
  },

  // 获取Agent列表
  getAgents: (userId: number) => {
    return apiClient.get<Agent[]>(`/agents?userId=${userId}`);
  },

  // 发布Agent
  publishAgent: (id: number) => {
    return apiClient.post<Agent>(`/agents/${id}/publish`);
  },

  // 归档Agent
  archiveAgent: (id: number) => {
    return apiClient.post<Agent>(`/agents/${id}/archive`);
  },

  // 复制Agent
  cloneAgent: (id: number, newOwnerId: number) => {
    return apiClient.post<Agent>(`/agents/${id}/clone?newOwnerId=${newOwnerId}`);
  },

  // 验证Agent配置
  validateAgent: (id: number) => {
    return apiClient.post<boolean>(`/agents/${id}/validate`);
  },

  // 执行Agent (同步)
  executeAgent: (id: number, request: AgentExecutionRequest) => {
    return apiClient.post<AgentExecutionResponse>(`/agents/${id}/execute`, request);
  },

  // 执行Agent (流式)
  executeAgentStream: (id: number, request: AgentExecutionRequest) => {
    return new EventSource(
      `${API_BASE_URL}/agents/${id}/execute/stream`,
      {
        withCredentials: true,
      }
    );
  },
};

export default agentService;
