{"name": "agent-v3-frontend", "version": "1.0.0", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "axios": "^1.6.0", "react-router-dom": "^6.8.0", "styled-components": "^6.1.0", "@types/styled-components": "^5.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}