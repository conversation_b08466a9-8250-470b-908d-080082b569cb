#!/bin/bash

echo "🚀 启动 Agent V3 项目..."

# 检查Java版本
echo "检查Java版本..."
java -version

# 检查PostgreSQL连接
echo "检查PostgreSQL连接..."
pg_isready -h 127.0.0.1 -p 5432 -U yilin

if [ $? -ne 0 ]; then
    echo "❌ PostgreSQL连接失败，请确保数据库正在运行"
    echo "可以使用以下命令启动PostgreSQL:"
    echo "brew services start postgresql"
    exit 1
fi

# 创建数据库（如果不存在）
echo "创建数据库（如果不存在）..."
createdb -h 127.0.0.1 -p 5432 -U yilin agent_v2 2>/dev/null || echo "数据库已存在"

# 编译项目
echo "编译项目..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

# 运行测试
echo "运行测试..."
mvn test

if [ $? -ne 0 ]; then
    echo "⚠️  测试失败，但继续启动应用"
fi

# 启动应用
echo "启动应用..."
echo "🌐 应用将在 http://localhost:8080 启动"
echo "📱 前端页面: http://localhost:8080"
echo "🔧 H2控制台: http://localhost:8080/h2-console (如果使用H2)"
echo ""
echo "按 Ctrl+C 停止应用"

mvn spring-boot:run
