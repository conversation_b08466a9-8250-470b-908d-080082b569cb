#!/bin/bash

echo "🧪 测试 Agent V3 API"
echo "===================="

BASE_URL="http://localhost:8080"

# 测试1: 创建Agent
echo "1. 创建测试Agent..."
AGENT_RESPONSE=$(curl -s -X POST "$BASE_URL/v1/agents" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "curl测试Agent",
    "description": "通过curl创建的测试Agent",
    "mode": "FUNCTION_CALLING",
    "systemPrompt": "你是一个友好的AI助手，请简洁地回答用户问题。",
    "openingStatement": "你好！我是curl测试Agent。",
    "creatorId": 1,
    "modelConfig": {
      "modelName": "qwen-turbo",
      "temperature": 0.7,
      "maxTokens": 1000,
      "topP": 0.9,
      "frequencyPenalty": 0.0,
      "presencePenalty": 0.0,
      "streamEnabled": true
    }
  }')

echo "创建Agent响应: $AGENT_RESPONSE"

# 提取Agent ID
AGENT_ID=$(echo $AGENT_RESPONSE | grep -o '"id":[0-9]*' | grep -o '[0-9]*')
echo "Agent ID: $AGENT_ID"

if [ -z "$AGENT_ID" ]; then
    echo "❌ 创建Agent失败，无法获取ID"
    exit 1
fi

echo "✅ Agent创建成功，ID: $AGENT_ID"
echo ""

# 测试2: 获取Agent详情
echo "2. 获取Agent详情..."
curl -s -X GET "$BASE_URL/v1/agents/$AGENT_ID" | jq '.' || echo "获取Agent详情响应: $(curl -s -X GET "$BASE_URL/v1/agents/$AGENT_ID")"
echo ""

# 测试3: 同步执行Agent
echo "3. 测试同步执行Agent..."
SYNC_RESPONSE=$(curl -s -X POST "$BASE_URL/v1/agents/$AGENT_ID/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "你好，请简单介绍一下自己",
    "userId": 1
  }')

echo "同步执行响应: $SYNC_RESPONSE"
echo ""

# 测试4: 流式执行Agent
echo "4. 测试流式执行Agent..."
echo "发送流式请求..."

curl -s -X POST "$BASE_URL/v1/agents/$AGENT_ID/execute/stream" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "query": "请告诉我今天是星期几，并简单解释一下你的功能",
    "userId": 1,
    "stream": true
  }' \
  --no-buffer | head -20

echo ""
echo "✅ 流式测试完成"
echo ""

# 测试5: 验证Agent配置
echo "5. 验证Agent配置..."
VALIDATE_RESPONSE=$(curl -s -X POST "$BASE_URL/v1/agents/$AGENT_ID/validate")
echo "验证响应: $VALIDATE_RESPONSE"
echo ""

echo "🎉 所有测试完成！"
